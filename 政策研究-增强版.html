<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>政策研究 - 大模型深度分析系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <script src="https://d3js.org/d3.v7.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .timeline-item {
        position: relative;
        padding-left: 2rem;
      }
      .timeline-item::before {
        content: '';
        position: absolute;
        left: 0.5rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, #31969A, #80CCE3);
      }
      .timeline-item::after {
        content: '';
        position: absolute;
        left: 0.25rem;
        top: 1rem;
        width: 0.5rem;
        height: 0.5rem;
        background: #31969A;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #31969A;
      }
      .timeline-item:last-child::before {
        background: linear-gradient(to bottom, #31969A 50%, transparent 50%);
      }
      .matrix-cell {
        transition: all 0.3s ease;
      }
      .matrix-cell:hover {
        transform: scale(1.05);
        z-index: 10;
      }
      .ai-analysis-badge {
        background: linear-gradient(135deg, #31969A, #80CCE3);
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
      }
      .network-node {
        cursor: pointer;
        transition: all 0.3s ease;
      }
      .network-node:hover {
        stroke-width: 3px;
      }
      .network-link {
        stroke: #80CCE3;
        stroke-opacity: 0.6;
        stroke-width: 2px;
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-brain text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">政策研究 - 大模型深度分析</h1>
          <p class="text-sm text-text-secondary">AI驱动的政策关联性与差异性分析系统</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="ai-analysis-badge text-white px-3 py-1 rounded-full text-sm">
          <i class="fa fa-magic mr-1"></i> AI分析中
        </div>
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span class="text-text-primary">2025年8月5日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 控制面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-text-primary">政策研究项目管理</h2>
        <button id="btn-new-analysis" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
          <i class="fa fa-plus mr-2"></i> 新建AI分析项目
        </button>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">研究主题</label>
          <input type="text" placeholder="输入研究主题关键词" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">分析类型</label>
          <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
            <option value="">全部类型</option>
            <option value="evolution">政策演变分析</option>
            <option value="comparison">政策对比分析</option>
            <option value="correlation">关联性分析</option>
            <option value="impact">影响评估</option>
          </select>
        </div>
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">关联政策</label>
          <input type="text" placeholder="输入政策名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>
        <div class="flex flex-col justify-end">
          <button class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-2"></i> 智能搜索
          </button>
        </div>
      </div>
    </div>

    <!-- 研究项目列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">AI分析项目列表</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-secondary/20 text-secondary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出报告
          </button>
          <button class="px-4 py-2 bg-accent/20 text-accent rounded-lg border border-accent/30 hover:bg-accent/30 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 刷新分析
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full border-collapse border border-border">
          <thead>
            <tr class="bg-light">
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">项目名称</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">分析类型</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">涉及政策数量</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">AI分析状态</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">创建时间</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">乡村振兴能源政策演变轨迹分析</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary/10 text-primary">
                  <i class="fa fa-timeline mr-1"></i> 演变分析
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">15</span> 项政策
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-success/10 text-success">
                  <i class="fa fa-check-circle mr-1"></i> 分析完成
                </span>
              </td>
              <td class="border border-border px-4 py-3">2025-08-01</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" onclick="showAnalysisDetail('evolution')">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">新能源政策差异性对比研究</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary/10 text-secondary">
                  <i class="fa fa-balance-scale mr-1"></i> 对比分析
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">8</span> 项政策
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-success/10 text-success">
                  <i class="fa fa-check-circle mr-1"></i> 分析完成
                </span>
              </td>
              <td class="border border-border px-4 py-3">2025-07-28</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" onclick="showAnalysisDetail('comparison')">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">电力政策关联网络图谱构建</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-accent/10 text-accent">
                  <i class="fa fa-sitemap mr-1"></i> 关联分析
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">22</span> 项政策
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-warning/10 text-warning">
                  <i class="fa fa-spinner fa-spin mr-1"></i> 分析中
                </span>
              </td>
              <td class="border border-border px-4 py-3">2025-08-03</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" onclick="showAnalysisDetail('network')">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">碳中和目标政策影响评估</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-warning/10 text-warning">
                  <i class="fa fa-assessment mr-1"></i> 影响评估
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">12</span> 项政策
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-success/10 text-success">
                  <i class="fa fa-check-circle mr-1"></i> 分析完成
                </span>
              </td>
              <td class="border border-border px-4 py-3">2025-07-30</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" onclick="alert('详情功能开发中')">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">智能电网建设政策演变研究</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary/10 text-primary">
                  <i class="fa fa-timeline mr-1"></i> 演变分析
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">9</span> 项政策
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-success/10 text-success">
                  <i class="fa fa-check-circle mr-1"></i> 分析完成
                </span>
              </td>
              <td class="border border-border px-4 py-3">2025-07-25</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" onclick="alert('详情功能开发中')">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">储能技术政策支持体系对比</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary/10 text-secondary">
                  <i class="fa fa-balance-scale mr-1"></i> 对比分析
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">6</span> 项政策
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-warning/10 text-warning">
                  <i class="fa fa-spinner fa-spin mr-1"></i> 分析中
                </span>
              </td>
              <td class="border border-border px-4 py-3">2025-07-22</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" onclick="alert('详情功能开发中')">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">农村电网改造升级效果评估</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-warning/10 text-warning">
                  <i class="fa fa-assessment mr-1"></i> 影响评估
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">11</span> 项政策
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-success/10 text-success">
                  <i class="fa fa-check-circle mr-1"></i> 分析完成
                </span>
              </td>
              <td class="border border-border px-4 py-3">2025-07-20</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" onclick="alert('详情功能开发中')">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">分布式能源政策关联性分析</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-accent/10 text-accent">
                  <i class="fa fa-sitemap mr-1"></i> 关联分析
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">14</span> 项政策
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-success/10 text-success">
                  <i class="fa fa-check-circle mr-1"></i> 分析完成
                </span>
              </td>
              <td class="border border-border px-4 py-3">2025-07-18</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" onclick="alert('详情功能开发中')">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">电力市场化改革政策对比研究</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary/10 text-secondary">
                  <i class="fa fa-balance-scale mr-1"></i> 对比分析
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">7</span> 项政策
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-success/10 text-success">
                  <i class="fa fa-check-circle mr-1"></i> 分析完成
                </span>
              </td>
              <td class="border border-border px-4 py-3">2025-07-15</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" onclick="alert('详情功能开发中')">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">清洁能源消纳政策演变轨迹</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary/10 text-primary">
                  <i class="fa fa-timeline mr-1"></i> 演变分析
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">10</span> 项政策
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-warning/10 text-warning">
                  <i class="fa fa-spinner fa-spin mr-1"></i> 分析中
                </span>
              </td>
              <td class="border border-border px-4 py-3">2025-07-12</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" onclick="alert('详情功能开发中')">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 10 条，每页显示 10 条，第 1 页/共 1 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors" disabled>
            上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white rounded">1</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors" disabled>
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>

  </div>

  <!-- 分析详情模态框 -->
  <div id="analysis-modal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden p-4">
    <div class="bg-lighter border border-border rounded-xl w-full max-w-7xl max-h-[95vh] overflow-hidden transform transition-all duration-300 scale-95 opacity-0" id="analysis-modal-content">
      <div class="flex justify-between items-center p-6 border-b border-border flex-shrink-0">
        <h3 class="text-2xl font-semibold text-text-primary" id="analysis-title">AI政策分析详情</h3>
        <button id="close-analysis-modal" class="text-text-secondary hover:text-primary transition-colors">
          <i class="fa fa-times text-xl"></i>
        </button>
      </div>

      <div class="p-6 overflow-y-auto" style="max-height: calc(95vh - 120px);">
        <!-- 政策演变时间轴 -->
        <div id="evolution-analysis" class="hidden">
          <div class="mb-6">
            <h4 class="text-xl font-semibold text-text-primary mb-4 flex items-center">
              <i class="fa fa-timeline text-primary mr-2"></i>
              乡村振兴能源政策演变时间轴
              <span class="ml-3 px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">
                <i class="fa fa-magic mr-1"></i> AI深度分析
              </span>
            </h4>

            <div class="bg-light rounded-lg p-6 mb-6">
              <div class="space-y-6">
                <div class="timeline-item">
                  <div class="bg-white rounded-lg p-4 shadow-sm border border-border">
                    <div class="flex justify-between items-start mb-2">
                      <h5 class="font-semibold text-text-primary">2018年 - 乡村振兴战略规划</h5>
                      <span class="text-sm text-text-secondary bg-primary/10 px-2 py-1 rounded">起始阶段</span>
                    </div>
                    <p class="text-text-secondary mb-2">《乡村振兴战略规划（2018-2022年）》发布，首次系统性提出农村能源发展目标</p>
                    <div class="flex items-center text-sm text-primary">
                      <i class="fa fa-lightbulb mr-1"></i>
                      <span>AI分析：奠定了农村能源现代化的政策基础</span>
                    </div>
                  </div>
                </div>

                <div class="timeline-item">
                  <div class="bg-white rounded-lg p-4 shadow-sm border border-border">
                    <div class="flex justify-between items-start mb-2">
                      <h5 class="font-semibold text-text-primary">2020年 - 农村电网巩固提升工程</h5>
                      <span class="text-sm text-text-secondary bg-secondary/10 px-2 py-1 rounded">发展阶段</span>
                    </div>
                    <p class="text-text-secondary mb-2">启动新一轮农村电网改造升级，重点解决深度贫困地区电网薄弱问题</p>
                    <div class="flex items-center text-sm text-primary">
                      <i class="fa fa-lightbulb mr-1"></i>
                      <span>AI分析：政策重心从规划转向具体实施，体现精准扶贫理念</span>
                    </div>
                  </div>
                </div>

                <div class="timeline-item">
                  <div class="bg-white rounded-lg p-4 shadow-sm border border-border">
                    <div class="flex justify-between items-start mb-2">
                      <h5 class="font-semibold text-text-primary">2021年 - 碳达峰碳中和目标</h5>
                      <span class="text-sm text-text-secondary bg-accent/10 px-2 py-1 rounded">转型阶段</span>
                    </div>
                    <p class="text-text-secondary mb-2">提出2030年碳达峰、2060年碳中和目标，农村能源绿色转型成为重点</p>
                    <div class="flex items-center text-sm text-primary">
                      <i class="fa fa-lightbulb mr-1"></i>
                      <span>AI分析：政策导向从基础建设转向绿色发展，体现可持续理念</span>
                    </div>
                  </div>
                </div>

                <div class="timeline-item">
                  <div class="bg-white rounded-lg p-4 shadow-sm border border-border">
                    <div class="flex justify-between items-start mb-2">
                      <h5 class="font-semibold text-text-primary">2023年 - 乡村振兴有效衔接</h5>
                      <span class="text-sm text-text-secondary bg-warning/10 px-2 py-1 rounded">深化阶段</span>
                    </div>
                    <p class="text-text-secondary mb-2">《关于实现巩固拓展脱贫攻坚成果同乡村振兴有效衔接的意见》，强化能源保障</p>
                    <div class="flex items-center text-sm text-primary">
                      <i class="fa fa-lightbulb mr-1"></i>
                      <span>AI分析：政策体系更加完善，注重长效机制建设</span>
                    </div>
                  </div>
                </div>

                <div class="timeline-item">
                  <div class="bg-white rounded-lg p-4 shadow-sm border border-border">
                    <div class="flex justify-between items-start mb-2">
                      <h5 class="font-semibold text-text-primary">2025年 - 新能源乡村振兴</h5>
                      <span class="text-sm text-text-secondary bg-success/10 px-2 py-1 rounded">创新阶段</span>
                    </div>
                    <p class="text-text-secondary mb-2">推进分布式光伏、风电等新能源在农村地区规模化应用</p>
                    <div class="flex items-center text-sm text-primary">
                      <i class="fa fa-lightbulb mr-1"></i>
                      <span>AI分析：政策重点转向新技术应用，体现创新驱动发展</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- AI分析总结 -->
            <div class="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-6">
              <h5 class="font-semibold text-text-primary mb-3 flex items-center">
                <i class="fa fa-brain text-primary mr-2"></i>
                AI深度分析总结
              </h5>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h6 class="font-medium text-text-primary mb-2">政策演变特征</h6>
                  <ul class="text-text-secondary space-y-1 text-sm">
                    <li>• 从基础设施建设向绿色转型发展</li>
                    <li>• 政策目标日趋精准化和系统化</li>
                    <li>• 技术创新成为重要驱动力</li>
                  </ul>
                </div>
                <div>
                  <h6 class="font-medium text-text-primary mb-2">发展趋势预测</h6>
                  <ul class="text-text-secondary space-y-1 text-sm">
                    <li>• 新能源技术将加速普及应用</li>
                    <li>• 政策支持力度将持续加强</li>
                    <li>• 数字化智能化成为新方向</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 政策对比矩阵 -->
        <div id="comparison-analysis" class="hidden">
          <div class="mb-6">
            <h4 class="text-xl font-semibold text-text-primary mb-4 flex items-center">
              <i class="fa fa-balance-scale text-primary mr-2"></i>
              新能源政策差异性对比矩阵
              <span class="ml-3 px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">
                <i class="fa fa-magic mr-1"></i> AI智能对比
              </span>
            </h4>

            <div class="bg-light rounded-lg p-6 mb-6">
              <div class="overflow-x-auto">
                <table class="w-full min-w-[800px]">
                  <thead>
                    <tr>
                      <th class="text-left p-3 font-semibold text-text-primary">对比维度</th>
                      <th class="text-center p-3 font-semibold text-text-primary bg-primary/10">分布式光伏政策</th>
                      <th class="text-center p-3 font-semibold text-text-primary bg-secondary/10">风电发展政策</th>
                      <th class="text-center p-3 font-semibold text-text-primary bg-accent/10">储能技术政策</th>
                      <th class="text-center p-3 font-semibold text-text-primary bg-warning/10">AI相似度</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-border">
                      <td class="p-3 font-medium">政策目标</td>
                      <td class="p-3 text-center matrix-cell bg-primary/5">
                        <div class="text-sm">户用光伏普及</div>
                        <div class="text-xs text-text-secondary">装机目标明确</div>
                      </td>
                      <td class="p-3 text-center matrix-cell bg-secondary/5">
                        <div class="text-sm">集中式开发</div>
                        <div class="text-xs text-text-secondary">规模化发展</div>
                      </td>
                      <td class="p-3 text-center matrix-cell bg-accent/5">
                        <div class="text-sm">技术突破</div>
                        <div class="text-xs text-text-secondary">产业化应用</div>
                      </td>
                      <td class="p-3 text-center">
                        <span class="px-2 py-1 bg-success/10 text-success rounded text-sm">85%</span>
                      </td>
                    </tr>
                    <tr class="border-t border-border">
                      <td class="p-3 font-medium">支持方式</td>
                      <td class="p-3 text-center matrix-cell bg-primary/5">
                        <div class="text-sm">补贴+税收优惠</div>
                        <div class="text-xs text-text-secondary">直接财政支持</div>
                      </td>
                      <td class="p-3 text-center matrix-cell bg-secondary/5">
                        <div class="text-sm">绿证+配额制</div>
                        <div class="text-xs text-text-secondary">市场化机制</div>
                      </td>
                      <td class="p-3 text-center matrix-cell bg-accent/5">
                        <div class="text-sm">研发资助</div>
                        <div class="text-xs text-text-secondary">技术创新支持</div>
                      </td>
                      <td class="p-3 text-center">
                        <span class="px-2 py-1 bg-warning/10 text-warning rounded text-sm">62%</span>
                      </td>
                    </tr>
                    <tr class="border-t border-border">
                      <td class="p-3 font-medium">实施周期</td>
                      <td class="p-3 text-center matrix-cell bg-primary/5">
                        <div class="text-sm">2021-2025</div>
                        <div class="text-xs text-text-secondary">5年规划</div>
                      </td>
                      <td class="p-3 text-center matrix-cell bg-secondary/5">
                        <div class="text-sm">2020-2030</div>
                        <div class="text-xs text-text-secondary">10年规划</div>
                      </td>
                      <td class="p-3 text-center matrix-cell bg-accent/5">
                        <div class="text-sm">2022-2027</div>
                        <div class="text-xs text-text-secondary">5年规划</div>
                      </td>
                      <td class="p-3 text-center">
                        <span class="px-2 py-1 bg-success/10 text-success rounded text-sm">78%</span>
                      </td>
                    </tr>
                    <tr class="border-t border-border">
                      <td class="p-3 font-medium">重点区域</td>
                      <td class="p-3 text-center matrix-cell bg-primary/5">
                        <div class="text-sm">农村地区</div>
                        <div class="text-xs text-text-secondary">分散布局</div>
                      </td>
                      <td class="p-3 text-center matrix-cell bg-secondary/5">
                        <div class="text-sm">三北地区</div>
                        <div class="text-xs text-text-secondary">资源富集区</div>
                      </td>
                      <td class="p-3 text-center matrix-cell bg-accent/5">
                        <div class="text-sm">电网节点</div>
                        <div class="text-xs text-text-secondary">战略位置</div>
                      </td>
                      <td class="p-3 text-center">
                        <span class="px-2 py-1 bg-danger/10 text-danger rounded text-sm">45%</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- AI对比分析结论 -->
            <div class="bg-gradient-to-r from-secondary/10 to-accent/10 rounded-lg p-6">
              <h5 class="font-semibold text-text-primary mb-3 flex items-center">
                <i class="fa fa-chart-bar text-secondary mr-2"></i>
                AI对比分析结论
              </h5>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h6 class="font-medium text-text-primary mb-2">共同特征</h6>
                  <ul class="text-text-secondary space-y-1 text-sm">
                    <li>• 都强调绿色低碳发展</li>
                    <li>• 政策支持力度较大</li>
                    <li>• 注重技术创新驱动</li>
                  </ul>
                </div>
                <div>
                  <h6 class="font-medium text-text-primary mb-2">差异分析</h6>
                  <ul class="text-text-secondary space-y-1 text-sm">
                    <li>• 应用场景定位不同</li>
                    <li>• 支持方式各有侧重</li>
                    <li>• 发展阶段存在差异</li>
                  </ul>
                </div>
                <div>
                  <h6 class="font-medium text-text-primary mb-2">协同建议</h6>
                  <ul class="text-text-secondary space-y-1 text-sm">
                    <li>• 加强政策统筹协调</li>
                    <li>• 推进技术融合发展</li>
                    <li>• 完善配套支持体系</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 政策关联网络图谱 -->
        <div id="network-analysis" class="hidden">
          <div class="mb-6">
            <h4 class="text-xl font-semibold text-text-primary mb-4 flex items-center">
              <i class="fa fa-sitemap text-primary mr-2"></i>
              电力政策关联网络图谱
              <span class="ml-3 px-3 py-1 bg-warning/10 text-warning rounded-full text-sm">
                <i class="fa fa-spinner fa-spin mr-1"></i> AI构建中
              </span>
            </h4>

            <div class="bg-light rounded-lg p-6 mb-6">
              <div id="network-graph" class="w-full h-80 border border-border rounded-lg bg-white relative">
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="text-center">
                    <i class="fa fa-spinner fa-spin text-4xl text-primary mb-4"></i>
                    <p class="text-text-secondary">AI正在分析政策关联关系...</p>
                    <p class="text-sm text-text-tertiary mt-2">预计还需要 2 分钟</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 网络分析指标 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div class="bg-white rounded-lg p-4 border border-border">
                <div class="text-center">
                  <div class="text-2xl font-bold text-primary">22</div>
                  <div class="text-sm text-text-secondary">政策节点</div>
                </div>
              </div>
              <div class="bg-white rounded-lg p-4 border border-border">
                <div class="text-center">
                  <div class="text-2xl font-bold text-secondary">156</div>
                  <div class="text-sm text-text-secondary">关联边</div>
                </div>
              </div>
              <div class="bg-white rounded-lg p-4 border border-border">
                <div class="text-center">
                  <div class="text-2xl font-bold text-accent">0.73</div>
                  <div class="text-sm text-text-secondary">网络密度</div>
                </div>
              </div>
              <div class="bg-white rounded-lg p-4 border border-border">
                <div class="text-center">
                  <div class="text-2xl font-bold text-warning">5</div>
                  <div class="text-sm text-text-secondary">核心集群</div>
                </div>
              </div>
            </div>

            <!-- AI网络分析洞察 -->
            <div class="bg-gradient-to-r from-accent/10 to-warning/10 rounded-lg p-6">
              <h5 class="font-semibold text-text-primary mb-3 flex items-center">
                <i class="fa fa-eye text-accent mr-2"></i>
                AI网络分析洞察
              </h5>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h6 class="font-medium text-text-primary mb-2">核心政策识别</h6>
                  <ul class="text-text-secondary space-y-1 text-sm">
                    <li>• 《"十四五"现代能源体系规划》- 中心度最高</li>
                    <li>• 《碳达峰行动方案》- 影响力最广</li>
                    <li>• 《新能源发展实施方案》- 连接性最强</li>
                  </ul>
                </div>
                <div>
                  <h6 class="font-medium text-text-primary mb-2">政策集群分析</h6>
                  <ul class="text-text-secondary space-y-1 text-sm">
                    <li>• 新能源发展集群 - 8项政策</li>
                    <li>• 农村电网建设集群 - 6项政策</li>
                    <li>• 碳中和目标集群 - 5项政策</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 大模型政策分析系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 显示分析详情
      window.showAnalysisDetail = function(type) {
        const modal = document.getElementById('analysis-modal');
        const modalContent = document.getElementById('analysis-modal-content');
        const title = document.getElementById('analysis-title');

        // 隐藏所有分析内容
        document.getElementById('evolution-analysis').classList.add('hidden');
        document.getElementById('comparison-analysis').classList.add('hidden');
        document.getElementById('network-analysis').classList.add('hidden');

        // 显示对应的分析内容
        switch(type) {
          case 'evolution':
            document.getElementById('evolution-analysis').classList.remove('hidden');
            title.textContent = '政策演变时间轴分析';
            break;
          case 'comparison':
            document.getElementById('comparison-analysis').classList.remove('hidden');
            title.textContent = '政策差异性对比分析';
            break;
          case 'network':
            document.getElementById('network-analysis').classList.remove('hidden');
            title.textContent = '政策关联网络分析';
            // 模拟网络图谱加载
            setTimeout(() => {
              initNetworkGraph();
            }, 1000);
            break;
        }

        modal.classList.remove('hidden');
        setTimeout(() => {
          modalContent.classList.remove('scale-95', 'opacity-0');
          modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);
      };

      // 关闭分析详情模态框
      document.getElementById('close-analysis-modal').addEventListener('click', function() {
        const modal = document.getElementById('analysis-modal');
        const modalContent = document.getElementById('analysis-modal-content');

        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
          modal.classList.add('hidden');
        }, 300);
      });

      // 初始化网络图谱
      function initNetworkGraph() {
        const container = document.getElementById('network-graph');
        container.innerHTML = '';

        const width = container.clientWidth;
        const height = container.clientHeight;

        const svg = d3.select('#network-graph')
          .append('svg')
          .attr('width', width)
          .attr('height', height);

        // 模拟网络数据
        const nodes = [
          {id: '十四五规划', group: 1, size: 20},
          {id: '碳达峰方案', group: 1, size: 18},
          {id: '新能源政策', group: 2, size: 16},
          {id: '农村电网', group: 3, size: 14},
          {id: '分布式光伏', group: 2, size: 12},
          {id: '风电发展', group: 2, size: 12},
          {id: '储能技术', group: 2, size: 10},
          {id: '智能电网', group: 3, size: 10},
          {id: '乡村振兴', group: 4, size: 15},
          {id: '脱贫攻坚', group: 4, size: 13}
        ];

        const links = [
          {source: '十四五规划', target: '碳达峰方案', value: 3},
          {source: '十四五规划', target: '新能源政策', value: 2},
          {source: '新能源政策', target: '分布式光伏', value: 2},
          {source: '新能源政策', target: '风电发展', value: 2},
          {source: '新能源政策', target: '储能技术', value: 1},
          {source: '农村电网', target: '智能电网', value: 2},
          {source: '乡村振兴', target: '脱贫攻坚', value: 3},
          {source: '乡村振兴', target: '农村电网', value: 2},
          {source: '分布式光伏', target: '乡村振兴', value: 1}
        ];

        const simulation = d3.forceSimulation(nodes)
          .force('link', d3.forceLink(links).id(d => d.id).distance(100))
          .force('charge', d3.forceManyBody().strength(-300))
          .force('center', d3.forceCenter(width / 2, height / 2));

        const link = svg.append('g')
          .selectAll('line')
          .data(links)
          .enter().append('line')
          .attr('class', 'network-link')
          .attr('stroke-width', d => Math.sqrt(d.value) * 2);

        const node = svg.append('g')
          .selectAll('circle')
          .data(nodes)
          .enter().append('circle')
          .attr('class', 'network-node')
          .attr('r', d => d.size)
          .attr('fill', d => {
            const colors = ['#31969A', '#80CCE3', '#8199C7', '#E5CE66'];
            return colors[d.group - 1];
          })
          .attr('stroke', '#fff')
          .attr('stroke-width', 2)
          .call(d3.drag()
            .on('start', dragstarted)
            .on('drag', dragged)
            .on('end', dragended));

        const label = svg.append('g')
          .selectAll('text')
          .data(nodes)
          .enter().append('text')
          .text(d => d.id)
          .attr('font-size', 12)
          .attr('font-family', 'Inter, sans-serif')
          .attr('fill', '#1E293B')
          .attr('text-anchor', 'middle')
          .attr('dy', 4);

        simulation.on('tick', () => {
          link
            .attr('x1', d => d.source.x)
            .attr('y1', d => d.source.y)
            .attr('x2', d => d.target.x)
            .attr('y2', d => d.target.y);

          node
            .attr('cx', d => d.x)
            .attr('cy', d => d.y);

          label
            .attr('x', d => d.x)
            .attr('y', d => d.y);
        });

        function dragstarted(event, d) {
          if (!event.active) simulation.alphaTarget(0.3).restart();
          d.fx = d.x;
          d.fy = d.y;
        }

        function dragged(event, d) {
          d.fx = event.x;
          d.fy = event.y;
        }

        function dragended(event, d) {
          if (!event.active) simulation.alphaTarget(0);
          d.fx = null;
          d.fy = null;
        }
      }
    });
  </script>
</body>

</html>
