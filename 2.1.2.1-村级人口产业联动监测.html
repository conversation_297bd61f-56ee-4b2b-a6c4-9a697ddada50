<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 村级人口产业联动监测</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .monitoring-badge {
        background: linear-gradient(135deg, #31969A, #80CCE3);
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
      }
      .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-line-chart text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 村级人口产业联动监测</h1>
          <p class="text-sm text-text-secondary">人口-产业-用能联动关系动态监测分析</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="monitoring-badge text-white px-3 py-1 rounded-full text-sm">
          <i class="fa fa-refresh fa-spin mr-1"></i> 实时监测中
        </div>
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span class="text-text-primary">2025年8月5日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 监测概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">监测村庄</p>
            <h3 class="text-3xl font-bold mt-2 text-primary">156</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-home text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">覆盖38个区县</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">总人口</p>
            <h3 class="text-3xl font-bold mt-2 text-secondary">45.2万</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-secondary/10 flex items-center justify-center text-secondary">
            <i class="fa fa-users text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">较上月 +0.8%</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">返乡人口</p>
            <h3 class="text-3xl font-bold mt-2 text-accent">3.6万</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-reply text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">返乡率 8.0%</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">产业用电</p>
            <h3 class="text-3xl font-bold mt-2 text-warning">2.8亿</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-bolt text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">千瓦时/月</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">人均生活用电</p>
            <h3 class="text-3xl font-bold mt-2 text-success">186</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-success/10 flex items-center justify-center text-success">
            <i class="fa fa-lightbulb-o text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">千瓦时/人·月</div>
      </div>
    </div>

    <!-- 可视化仪表盘 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 人口产业联动趋势 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-4 flex items-center">
          <i class="fa fa-line-chart text-primary mr-2"></i>
          人口产业联动趋势
        </h3>
        <div class="chart-container">
          <canvas id="population-industry-chart"></canvas>
        </div>
      </div>

      <!-- 用能结构分析 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-4 flex items-center">
          <i class="fa fa-pie-chart text-primary mr-2"></i>
          用能结构分析
        </h3>
        <div class="chart-container">
          <canvas id="energy-structure-chart"></canvas>
        </div>
      </div>
    </div>

    <!-- 查询控制面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-text-primary">联动监测查询</h2>
        <button class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
          <i class="fa fa-refresh mr-2"></i> 刷新数据
        </button>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属区县</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部区县</option>
              <option value="wanzhou">万州区</option>
              <option value="fuling">涪陵区</option>
              <option value="qianjiang">黔江区</option>
              <option value="wushan">巫山县</option>
              <option value="fengjie">奉节县</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">村名</label>
          <input type="text" placeholder="输入村名关键词" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">监测周期</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="current">本月</option>
              <option value="quarter">本季度</option>
              <option value="year">本年度</option>
              <option value="custom">自定义</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col justify-end">
          <button class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-2"></i> 查询
          </button>
        </div>
      </div>
    </div>

    <!-- 监测数据列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">村级联动监测数据</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-secondary/20 text-secondary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出数据
          </button>
          <button class="px-4 py-2 bg-accent/20 text-accent rounded-lg border border-accent/30 hover:bg-accent/30 transition-colors">
            <i class="fa fa-bar-chart mr-1"></i> 分析报告
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full border-collapse border border-border">
          <thead>
            <tr class="bg-light">
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">村名</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">所属区县</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">常住人口数</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">返乡人口数</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">主导产业类型</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">产业规模</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">产业用电量</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">人均生活用电量</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">联动指数</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">龙凤村</td>
              <td class="border border-border px-4 py-3">万州区</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">2,856</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-accent">285</span>
                <span class="text-sm text-text-secondary ml-1">(10.0%)</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-leaf mr-1"></i> 特色农业
                </span>
              </td>
              <td class="border border-border px-4 py-3">1,200亩</td>
              <td class="border border-border px-4 py-3">
                <span class="text-warning font-bold">18.5万</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">195</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                  <i class="fa fa-arrow-up mr-1"></i> 85.6
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70" title="分析报告">
                  <i class="fa fa-bar-chart"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">青山村</td>
              <td class="border border-border px-4 py-3">黔江区</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">3,124</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-accent">218</span>
                <span class="text-sm text-text-secondary ml-1">(7.0%)</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-accent/10 text-accent">
                  <i class="fa fa-industry mr-1"></i> 乡村旅游
                </span>
              </td>
              <td class="border border-border px-4 py-3">15家民宿</td>
              <td class="border border-border px-4 py-3">
                <span class="text-warning font-bold">12.8万</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">168</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-secondary/10 text-secondary">
                  <i class="fa fa-arrow-up mr-1"></i> 78.3
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70" title="分析报告">
                  <i class="fa fa-bar-chart"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">桃花村</td>
              <td class="border border-border px-4 py-3">涪陵区</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">2,689</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-accent">322</span>
                <span class="text-sm text-text-secondary ml-1">(12.0%)</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-warning/10 text-warning">
                  <i class="fa fa-cogs mr-1"></i> 农产品加工
                </span>
              </td>
              <td class="border border-border px-4 py-3">3条生产线</td>
              <td class="border border-border px-4 py-3">
                <span class="text-warning font-bold">25.6万</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">203</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                  <i class="fa fa-arrow-up mr-1"></i> 92.1
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70" title="分析报告">
                  <i class="fa fa-bar-chart"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">金竹村</td>
              <td class="border border-border px-4 py-3">巫山县</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">1,945</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-accent">156</span>
                <span class="text-sm text-text-secondary ml-1">(8.0%)</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                  <i class="fa fa-sun-o mr-1"></i> 光伏发电
                </span>
              </td>
              <td class="border border-border px-4 py-3">2MW装机</td>
              <td class="border border-border px-4 py-3">
                <span class="text-warning font-bold">8.9万</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">142</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-accent/10 text-accent">
                  <i class="fa fa-arrow-up mr-1"></i> 71.8
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70" title="分析报告">
                  <i class="fa fa-bar-chart"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">梨花村</td>
              <td class="border border-border px-4 py-3">奉节县</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">2,234</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-accent">89</span>
                <span class="text-sm text-text-secondary ml-1">(4.0%)</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-apple mr-1"></i> 果树种植
                </span>
              </td>
              <td class="border border-border px-4 py-3">800亩果园</td>
              <td class="border border-border px-4 py-3">
                <span class="text-warning font-bold">6.2万</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">128</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-warning/10 text-warning">
                  <i class="fa fa-minus mr-1"></i> 58.4
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70" title="分析报告">
                  <i class="fa fa-bar-chart"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">翠竹村</td>
              <td class="border border-border px-4 py-3">石柱县</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">3,567</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-accent">428</span>
                <span class="text-sm text-text-secondary ml-1">(12.0%)</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-accent/10 text-accent">
                  <i class="fa fa-shopping-cart mr-1"></i> 电商物流
                </span>
              </td>
              <td class="border border-border px-4 py-3">5个服务点</td>
              <td class="border border-border px-4 py-3">
                <span class="text-warning font-bold">15.3万</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">178</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-secondary/10 text-secondary">
                  <i class="fa fa-arrow-up mr-1"></i> 82.7
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70" title="分析报告">
                  <i class="fa fa-bar-chart"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">向阳村</td>
              <td class="border border-border px-4 py-3">酉阳县</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">2,891</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-accent">231</span>
                <span class="text-sm text-text-secondary ml-1">(8.0%)</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-pagelines mr-1"></i> 中药材种植
                </span>
              </td>
              <td class="border border-border px-4 py-3">600亩基地</td>
              <td class="border border-border px-4 py-3">
                <span class="text-warning font-bold">11.7万</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">156</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-accent/10 text-accent">
                  <i class="fa fa-arrow-up mr-1"></i> 69.5
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70" title="分析报告">
                  <i class="fa fa-bar-chart"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">清泉村</td>
              <td class="border border-border px-4 py-3">秀山县</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">1,678</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-accent">134</span>
                <span class="text-sm text-text-secondary ml-1">(8.0%)</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-warning/10 text-warning">
                  <i class="fa fa-cutlery mr-1"></i> 食品加工
                </span>
              </td>
              <td class="border border-border px-4 py-3">2个加工厂</td>
              <td class="border border-border px-4 py-3">
                <span class="text-warning font-bold">9.8万</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">165</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-secondary/10 text-secondary">
                  <i class="fa fa-arrow-up mr-1"></i> 75.2
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70" title="分析报告">
                  <i class="fa fa-bar-chart"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">红岩村</td>
              <td class="border border-border px-4 py-3">彭水县</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">2,456</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-accent">196</span>
                <span class="text-sm text-text-secondary ml-1">(8.0%)</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-tree mr-1"></i> 林下经济
                </span>
              </td>
              <td class="border border-border px-4 py-3">1,500亩林地</td>
              <td class="border border-border px-4 py-3">
                <span class="text-warning font-bold">7.3万</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">149</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-accent/10 text-accent">
                  <i class="fa fa-arrow-up mr-1"></i> 66.8
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70" title="分析报告">
                  <i class="fa fa-bar-chart"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">松柏村</td>
              <td class="border border-border px-4 py-3">城口县</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">1,823</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-accent">109</span>
                <span class="text-sm text-text-secondary ml-1">(6.0%)</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                  <i class="fa fa-tint mr-1"></i> 水产养殖
                </span>
              </td>
              <td class="border border-border px-4 py-3">50亩鱼塘</td>
              <td class="border border-border px-4 py-3">
                <span class="text-warning font-bold">5.6万</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">134</span>
                <span class="text-sm text-text-secondary">kWh</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-warning/10 text-warning">
                  <i class="fa fa-minus mr-1"></i> 62.1
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70" title="分析报告">
                  <i class="fa fa-bar-chart"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 156 条，每页显示 10 条，第 1 页/共 16 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors" disabled>
            上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white rounded">1</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">2</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">3</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 村级人口产业联动监测系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 人口产业联动趋势图表
      const populationIndustryCtx = document.getElementById('population-industry-chart').getContext('2d');
      const populationIndustryChart = new Chart(populationIndustryCtx, {
        type: 'line',
        data: {
          labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
          datasets: [
            {
              label: '常住人口(万人)',
              data: [44.8, 44.9, 45.0, 45.1, 45.0, 45.1, 45.2],
              borderColor: '#31969A',
              backgroundColor: 'rgba(49, 150, 154, 0.1)',
              tension: 0.4,
              yAxisID: 'y'
            },
            {
              label: '返乡人口(万人)',
              data: [2.8, 3.1, 3.3, 3.4, 3.5, 3.6, 3.6],
              borderColor: '#8199C7',
              backgroundColor: 'rgba(129, 153, 199, 0.1)',
              tension: 0.4,
              yAxisID: 'y'
            },
            {
              label: '产业用电量(亿kWh)',
              data: [2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8],
              borderColor: '#E5CE66',
              backgroundColor: 'rgba(229, 206, 102, 0.1)',
              tension: 0.4,
              yAxisID: 'y1'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            mode: 'index',
            intersect: false,
          },
          scales: {
            x: {
              display: true,
              title: {
                display: true,
                text: '月份'
              }
            },
            y: {
              type: 'linear',
              display: true,
              position: 'left',
              title: {
                display: true,
                text: '人口(万人)'
              },
            },
            y1: {
              type: 'linear',
              display: true,
              position: 'right',
              title: {
                display: true,
                text: '用电量(亿kWh)'
              },
              grid: {
                drawOnChartArea: false,
              },
            }
          },
          plugins: {
            legend: {
              position: 'top',
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1E293B',
              bodyColor: '#64748B',
              borderColor: 'rgba(49, 150, 154, 0.2)',
              borderWidth: 1,
            }
          }
        }
      });

      // 用能结构分析饼图
      const energyStructureCtx = document.getElementById('energy-structure-chart').getContext('2d');
      const energyStructureChart = new Chart(energyStructureCtx, {
        type: 'doughnut',
        data: {
          labels: ['农业生产用电', '工业加工用电', '商业服务用电', '居民生活用电', '公共设施用电'],
          datasets: [{
            data: [35, 28, 15, 18, 4],
            backgroundColor: [
              '#31969A',
              '#80CCE3',
              '#8199C7',
              '#E5CE66',
              '#10B981'
            ],
            borderWidth: 0,
            hoverOffset: 10
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 20,
                usePointStyle: true,
                font: {
                  size: 12
                }
              }
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1E293B',
              bodyColor: '#64748B',
              borderColor: 'rgba(49, 150, 154, 0.2)',
              borderWidth: 1,
              padding: 16,
              callbacks: {
                label: function(context) {
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((context.raw / total) * 100).toFixed(1);
                  return `${context.label}: ${percentage}%`;
                }
              }
            }
          },
          cutout: '60%'
        }
      });

      // 刷新数据按钮
      document.querySelector('.bg-primary').addEventListener('click', function() {
        // 模拟数据刷新
        this.innerHTML = '<i class="fa fa-refresh fa-spin mr-2"></i> 刷新中...';
        this.disabled = true;

        setTimeout(() => {
          this.innerHTML = '<i class="fa fa-refresh mr-2"></i> 刷新数据';
          this.disabled = false;

          // 显示刷新成功提示
          const toast = document.createElement('div');
          toast.className = 'fixed top-4 right-4 bg-success text-white px-4 py-2 rounded-lg shadow-lg z-50';
          toast.innerHTML = '<i class="fa fa-check mr-2"></i>数据刷新成功';
          document.body.appendChild(toast);

          setTimeout(() => {
            toast.remove();
          }, 3000);
        }, 2000);
      });
    });
  </script>
</body>

</html>
