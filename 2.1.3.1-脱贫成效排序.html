<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>脱贫成效排序</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-bar-chart text-primary text-3xl"></i>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年7月17日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-text-primary mb-2">脱贫成效排序</h1>
      <p class="text-text-secondary">基于重点村脱贫成效分析综合指数的排名展示</p>
    </div>

    <!-- 控制面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属地市</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部地市</option>
              <option value="chongqing">重庆主城</option>
              <option value="wanzhou">万州区</option>
              <option value="fuling">涪陵区</option>
              <option value="qianjiang">黔江区</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属区县</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部区县</option>
              <option value="wushan">巫山县</option>
              <option value="fengjie">奉节县</option>
              <option value="wuxi">巫溪县</option>
              <option value="shizhu">石柱县</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">时间周期</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="current">本月</option>
              <option value="quarter">本季度</option>
              <option value="year">本年度</option>
              <option value="custom">自定义</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col justify-end">
          <button class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-2"></i> 查询
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">监测村庄总数</p>
            <h3 class="text-3xl font-bold mt-2">156 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-home text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">优秀村庄</p>
            <h3 class="text-3xl font-bold mt-2">42 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-star text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">90-100分</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">良好村庄</p>
            <h3 class="text-3xl font-bold mt-2">89 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-secondary/10 flex items-center justify-center text-secondary">
            <i class="fa fa-thumbs-up text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">70-89分</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">需关注村庄</p>
            <h3 class="text-3xl font-bold mt-2">25 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-danger/10 flex items-center justify-center text-danger">
            <i class="fa fa-exclamation-triangle text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">60分以下</div>
      </div>
    </div>

    <!-- 排序表格 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">脱贫成效排序列表</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-primary/20 text-primary rounded-lg border border-primary/30 hover:bg-primary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full border-collapse border border-border">
          <thead>
            <tr class="bg-light">
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">排名</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">村庄名称</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">所属地市</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">区县</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">综合指数得分</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">产业振兴分指数</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">人口发展分指数</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">排名变化</th>
            </tr>
          </thead>
          <tbody>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-warning text-white rounded-full font-bold">1</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">龙凤村</td>
              <td class="border border-border px-4 py-3">万州区</td>
              <td class="border border-border px-4 py-3">龙驹镇</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">95.8</span>
              </td>
              <td class="border border-border px-4 py-3">92.3</td>
              <td class="border border-border px-4 py-3">98.5</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary flex items-center">
                  <i class="fa fa-arrow-up mr-1"></i> +2
                </span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-secondary text-white rounded-full font-bold">2</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">青山村</td>
              <td class="border border-border px-4 py-3">黔江区</td>
              <td class="border border-border px-4 py-3">石会镇</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">94.2</span>
              </td>
              <td class="border border-border px-4 py-3">96.1</td>
              <td class="border border-border px-4 py-3">91.8</td>
              <td class="border border-border px-4 py-3">
                <span class="text-text-secondary flex items-center">
                  <i class="fa fa-minus mr-1"></i> 0
                </span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-accent text-white rounded-full font-bold">3</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">桃花村</td>
              <td class="border border-border px-4 py-3">涪陵区</td>
              <td class="border border-border px-4 py-3">蔺市镇</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">93.7</span>
              </td>
              <td class="border border-border px-4 py-3">89.4</td>
              <td class="border border-border px-4 py-3">97.2</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary flex items-center">
                  <i class="fa fa-arrow-up mr-1"></i> +1
                </span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-text-secondary text-white rounded-full font-bold">4</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">金竹村</td>
              <td class="border border-border px-4 py-3">巫山县</td>
              <td class="border border-border px-4 py-3">大昌镇</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">92.1</span>
              </td>
              <td class="border border-border px-4 py-3">94.7</td>
              <td class="border border-border px-4 py-3">88.9</td>
              <td class="border border-border px-4 py-3">
                <span class="text-danger flex items-center">
                  <i class="fa fa-arrow-down mr-1"></i> -1
                </span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-text-secondary text-white rounded-full font-bold">5</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">梨花村</td>
              <td class="border border-border px-4 py-3">奉节县</td>
              <td class="border border-border px-4 py-3">白帝镇</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">91.5</span>
              </td>
              <td class="border border-border px-4 py-3">87.3</td>
              <td class="border border-border px-4 py-3">95.1</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary flex items-center">
                  <i class="fa fa-arrow-up mr-1"></i> +3
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 156 条，每页显示 5 条，第 1 页/共 32 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors" disabled>
            上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white rounded">1</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">2</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">3</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 保留所有权利.</p>
    </div>
  </footer>
</body>

</html>
