<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 策略研究 - 帮扶策略与产业振兴路径</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .strategy-badge {
        background: linear-gradient(135deg, #31969A, #80CCE3);
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-lightbulb text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 策略研究</h1>
          <p class="text-sm text-text-secondary">帮扶策略与产业振兴路径研究系统</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="strategy-badge text-white px-3 py-1 rounded-full text-sm">
          <i class="fa fa-cogs mr-1"></i> 策略库构建中
        </div>
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span class="text-text-primary">2025年8月5日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 策略统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">策略库总数</p>
            <h3 class="text-3xl font-bold mt-2 text-primary">156</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-database text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">已结构化录入</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">防返贫策略</p>
            <h3 class="text-3xl font-bold mt-2 text-secondary">68</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-secondary/10 flex items-center justify-center text-secondary">
            <i class="fa fa-shield text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">43.6% 占比</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">产业振兴策略</p>
            <h3 class="text-3xl font-bold mt-2 text-accent">88</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-industry text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">56.4% 占比</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">本月新增</p>
            <h3 class="text-3xl font-bold mt-2 text-success">12</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-success/10 flex items-center justify-center text-success">
            <i class="fa fa-plus-circle text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">较上月 +8</div>
      </div>
    </div>

    <!-- 查询控制面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-text-primary">策略库查询</h2>
        <button id="btn-new-strategy" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
          <i class="fa fa-plus mr-2"></i> 新建策略
        </button>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">策略主线</label>
          <div class="relative">
            <select id="strategy-mainline" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部主线</option>
              <option value="防返贫">防返贫</option>
              <option value="产业振兴">产业振兴</option>
              <option value="基础设施">基础设施建设</option>
              <option value="人才培养">人才培养</option>
              <option value="生态保护">生态保护</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">适用对象</label>
          <div class="relative">
            <select id="target-object" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部对象</option>
              <option value="重点监测村">重点监测村</option>
              <option value="脱贫户">脱贫户</option>
              <option value="边缘易致贫户">边缘易致贫户</option>
              <option value="农业合作社">农业合作社</option>
              <option value="村集体经济">村集体经济</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">策略名称</label>
          <input type="text" id="strategy-name" placeholder="输入策略名称关键词" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">关联政策</label>
          <input type="text" id="related-policy" placeholder="输入政策名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col justify-end">
          <button id="btn-search" class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-2"></i> 查询策略
          </button>
        </div>
      </div>
    </div>

    <!-- 策略列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">策略库列表</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-secondary/20 text-secondary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出策略
          </button>
          <button class="px-4 py-2 bg-accent/20 text-accent rounded-lg border border-accent/30 hover:bg-accent/30 transition-colors">
            <i class="fa fa-cogs mr-1"></i> 批量管理
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full border-collapse border border-border">
          <thead>
            <tr class="bg-light">
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">策略编号</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">策略名称</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">策略主线</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">适用对象</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">内容摘要</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">关联政策依据</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">适用场景</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-mono text-primary">ST-2025-001</td>
              <td class="border border-border px-4 py-3 font-medium">分布式光伏助力脱贫户增收策略</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary/10 text-secondary">
                  <i class="fa fa-shield mr-1"></i> 防返贫
                </span>
              </td>
              <td class="border border-border px-4 py-3">脱贫户</td>
              <td class="border border-border px-4 py-3 text-sm">通过户用光伏发电项目，为脱贫户提供稳定收益来源，年均增收3000-5000元</td>
              <td class="border border-border px-4 py-3 text-sm">《关于促进新时代新能源高质量发展的实施方案》</td>
              <td class="border border-border px-4 py-3 text-sm">适合有屋顶资源的农户，光照条件良好地区</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-mono text-primary">ST-2025-002</td>
              <td class="border border-border px-4 py-3 font-medium">农村电商产业振兴路径</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-accent/10 text-accent">
                  <i class="fa fa-industry mr-1"></i> 产业振兴
                </span>
              </td>
              <td class="border border-border px-4 py-3">农业合作社</td>
              <td class="border border-border px-4 py-3 text-sm">建设农村电商服务站，发展特色农产品线上销售，打造区域品牌</td>
              <td class="border border-border px-4 py-3 text-sm">《数字乡村发展战略纲要》</td>
              <td class="border border-border px-4 py-3 text-sm">网络基础设施完善，有特色农产品资源的村庄</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-mono text-primary">ST-2025-003</td>
              <td class="border border-border px-4 py-3 font-medium">边缘户动态监测预警机制</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary/10 text-secondary">
                  <i class="fa fa-shield mr-1"></i> 防返贫
                </span>
              </td>
              <td class="border border-border px-4 py-3">边缘易致贫户</td>
              <td class="border border-border px-4 py-3 text-sm">建立收入监测系统，设置预警阈值，及时发现返贫风险并启动帮扶</td>
              <td class="border border-border px-4 py-3 text-sm">《关于健全防止返贫动态监测和帮扶机制的指导意见》</td>
              <td class="border border-border px-4 py-3 text-sm">收入不稳定，存在返贫风险的农户</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-mono text-primary">ST-2025-004</td>
              <td class="border border-border px-4 py-3 font-medium">特色农业产业链延伸策略</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-accent/10 text-accent">
                  <i class="fa fa-industry mr-1"></i> 产业振兴
                </span>
              </td>
              <td class="border border-border px-4 py-3">村集体经济</td>
              <td class="border border-border px-4 py-3 text-sm">发展农产品深加工，建设冷链物流，提升产品附加值</td>
              <td class="border border-border px-4 py-3 text-sm">《关于促进乡村产业振兴的指导意见》</td>
              <td class="border border-border px-4 py-3 text-sm">有特色农业基础，具备加工条件的村庄</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-mono text-primary">ST-2025-005</td>
              <td class="border border-border px-4 py-3 font-medium">农村电网升级改造帮扶策略</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-warning/10 text-warning">
                  <i class="fa fa-wrench mr-1"></i> 基础设施
                </span>
              </td>
              <td class="border border-border px-4 py-3">重点监测村</td>
              <td class="border border-border px-4 py-3 text-sm">优先改造电网薄弱地区，提升供电可靠性，支撑产业发展</td>
              <td class="border border-border px-4 py-3 text-sm">《农村电网巩固提升工程实施方案》</td>
              <td class="border border-border px-4 py-3 text-sm">电网设施老旧，供电质量不稳定的村庄</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-mono text-primary">ST-2025-006</td>
              <td class="border border-border px-4 py-3 font-medium">乡村旅游产业发展路径</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-accent/10 text-accent">
                  <i class="fa fa-industry mr-1"></i> 产业振兴
                </span>
              </td>
              <td class="border border-border px-4 py-3">村集体经济</td>
              <td class="border border-border px-4 py-3 text-sm">依托自然资源和文化特色，发展民宿、农家乐等旅游业态</td>
              <td class="border border-border px-4 py-3 text-sm">《关于促进乡村旅游可持续发展的指导意见》</td>
              <td class="border border-border px-4 py-3 text-sm">有旅游资源，交通便利的村庄</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-mono text-primary">ST-2025-007</td>
              <td class="border border-border px-4 py-3 font-medium">技能培训就业帮扶策略</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-success/10 text-success">
                  <i class="fa fa-graduation-cap mr-1"></i> 人才培养
                </span>
              </td>
              <td class="border border-border px-4 py-3">脱贫户</td>
              <td class="border border-border px-4 py-3 text-sm">开展实用技能培训，提供就业指导，增强内生发展动力</td>
              <td class="border border-border px-4 py-3 text-sm">《关于加强脱贫人口稳岗就业工作的通知》</td>
              <td class="border border-border px-4 py-3 text-sm">有劳动能力但缺乏技能的脱贫人口</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-mono text-primary">ST-2025-008</td>
              <td class="border border-border px-4 py-3 font-medium">生态保护补偿机制</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-success/20 text-success">
                  <i class="fa fa-leaf mr-1"></i> 生态保护
                </span>
              </td>
              <td class="border border-border px-4 py-3">重点监测村</td>
              <td class="border border-border px-4 py-3 text-sm">建立生态护林员制度，通过生态保护获得稳定收入</td>
              <td class="border border-border px-4 py-3 text-sm">《关于建立健全生态产品价值实现机制的意见》</td>
              <td class="border border-border px-4 py-3 text-sm">生态环境良好，有保护需求的山区村庄</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-mono text-primary">ST-2025-009</td>
              <td class="border border-border px-4 py-3 font-medium">消费帮扶长效机制</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary/10 text-secondary">
                  <i class="fa fa-shield mr-1"></i> 防返贫
                </span>
              </td>
              <td class="border border-border px-4 py-3">农业合作社</td>
              <td class="border border-border px-4 py-3 text-sm">建立稳定的产销对接关系，拓宽农产品销售渠道</td>
              <td class="border border-border px-4 py-3 text-sm">《关于深化消费帮扶助力乡村振兴的指导意见》</td>
              <td class="border border-border px-4 py-3 text-sm">有优质农产品，需要销售渠道的合作社</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-mono text-primary">ST-2025-010</td>
              <td class="border border-border px-4 py-3 font-medium">数字化村务管理策略</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-accent/10 text-accent">
                  <i class="fa fa-industry mr-1"></i> 产业振兴
                </span>
              </td>
              <td class="border border-border px-4 py-3">村集体经济</td>
              <td class="border border-border px-4 py-3 text-sm">运用数字技术提升村务管理效率，促进治理现代化</td>
              <td class="border border-border px-4 py-3 text-sm">《数字乡村发展战略纲要》</td>
              <td class="border border-border px-4 py-3 text-sm">有数字化基础，管理相对规范的村庄</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/70 mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/70" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 156 条，每页显示 10 条，第 1 页/共 16 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors" disabled>
            上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white rounded">1</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">2</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">3</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 新建策略模态框 -->
  <div id="new-strategy-modal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden p-4">
    <div class="bg-lighter border border-border rounded-xl w-full max-w-4xl max-h-[95vh] overflow-hidden transform transition-all duration-300 scale-95 opacity-0" id="strategy-modal-content">
      <div class="flex justify-between items-center p-6 border-b border-border flex-shrink-0">
        <h3 class="text-2xl font-semibold text-text-primary">新建帮扶策略</h3>
        <button id="close-strategy-modal" class="text-text-secondary hover:text-primary transition-colors">
          <i class="fa fa-times text-xl"></i>
        </button>
      </div>

      <div class="p-6 overflow-y-auto" style="max-height: calc(95vh - 120px);">
        <form class="space-y-6">
          <!-- 基本信息 -->
          <div class="bg-light rounded-lg p-4">
            <h4 class="text-lg font-semibold text-text-primary mb-4">基本信息</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="text-text-secondary block mb-2">策略名称 *</label>
                <input type="text" id="new-strategy-name" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" placeholder="请输入策略名称">
              </div>
              <div>
                <label class="text-text-secondary block mb-2">策略编号 *</label>
                <input type="text" id="new-strategy-code" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" placeholder="系统自动生成" readonly>
              </div>
              <div>
                <label class="text-text-secondary block mb-2">策略主线 *</label>
                <select id="new-strategy-mainline" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
                  <option value="">请选择策略主线</option>
                  <option value="防返贫">防返贫</option>
                  <option value="产业振兴">产业振兴</option>
                  <option value="基础设施">基础设施建设</option>
                  <option value="人才培养">人才培养</option>
                  <option value="生态保护">生态保护</option>
                </select>
              </div>
              <div>
                <label class="text-text-secondary block mb-2">适用对象 *</label>
                <select id="new-target-object" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
                  <option value="">请选择适用对象</option>
                  <option value="重点监测村">重点监测村</option>
                  <option value="脱贫户">脱贫户</option>
                  <option value="边缘易致贫户">边缘易致贫户</option>
                  <option value="农业合作社">农业合作社</option>
                  <option value="村集体经济">村集体经济</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 策略内容 -->
          <div class="bg-light rounded-lg p-4">
            <h4 class="text-lg font-semibold text-text-primary mb-4">策略内容</h4>
            <div class="space-y-4">
              <div>
                <label class="text-text-secondary block mb-2">内容摘要 *</label>
                <textarea id="new-strategy-summary" rows="3" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请简要描述策略的核心内容和目标..."></textarea>
              </div>
              <div>
                <label class="text-text-secondary block mb-2">详细内容</label>
                <textarea id="new-strategy-detail" rows="6" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请详细描述策略的实施步骤、具体措施、预期效果等..."></textarea>
              </div>
              <div>
                <label class="text-text-secondary block mb-2">适用场景 *</label>
                <textarea id="new-strategy-scenario" rows="2" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请描述策略的适用条件和场景..."></textarea>
              </div>
            </div>
          </div>

          <!-- 政策依据 -->
          <div class="bg-light rounded-lg p-4">
            <h4 class="text-lg font-semibold text-text-primary mb-4">政策依据</h4>
            <div class="space-y-4">
              <div>
                <label class="text-text-secondary block mb-2">关联政策依据 *</label>
                <input type="text" id="new-related-policy" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" placeholder="请输入相关政策文件名称">
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="text-text-secondary block mb-2">政策发布机关</label>
                  <input type="text" id="new-policy-authority" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" placeholder="如：国务院、发改委等">
                </div>
                <div>
                  <label class="text-text-secondary block mb-2">政策发布时间</label>
                  <input type="date" id="new-policy-date" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
                </div>
              </div>
            </div>
          </div>

          <!-- 实施信息 -->
          <div class="bg-light rounded-lg p-4">
            <h4 class="text-lg font-semibold text-text-primary mb-4">实施信息</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="text-text-secondary block mb-2">预期效果</label>
                <input type="text" id="new-expected-effect" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" placeholder="如：年均增收3000-5000元">
              </div>
              <div>
                <label class="text-text-secondary block mb-2">实施周期</label>
                <select id="new-implementation-period" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
                  <option value="">请选择实施周期</option>
                  <option value="短期">短期（1年内）</option>
                  <option value="中期">中期（1-3年）</option>
                  <option value="长期">长期（3年以上）</option>
                </select>
              </div>
              <div>
                <label class="text-text-secondary block mb-2">优先级</label>
                <select id="new-priority" class="w-full bg-white border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
                  <option value="">请选择优先级</option>
                  <option value="高">高</option>
                  <option value="中">中</option>
                  <option value="低">低</option>
                </select>
              </div>
            </div>
          </div>
        </form>

        <div class="flex justify-end space-x-4 mt-6 pt-4 border-t border-border">
          <button id="cancel-new-strategy" class="px-6 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            取消
          </button>
          <button id="save-draft-strategy" class="px-6 py-3 bg-warning text-white rounded-lg border border-warning hover:bg-warning/90 transition-colors">
            保存草稿
          </button>
          <button id="confirm-new-strategy" class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-save mr-2"></i> 创建策略
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 策略研究系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 生成策略编号
      function generateStrategyCode() {
        const year = new Date().getFullYear();
        const randomNum = Math.floor(Math.random() * 900) + 100;
        return `ST-${year}-${randomNum.toString().padStart(3, '0')}`;
      }

      // 新建策略按钮
      document.getElementById('btn-new-strategy').addEventListener('click', function () {
        const modal = document.getElementById('new-strategy-modal');
        const modalContent = document.getElementById('strategy-modal-content');

        // 生成新的策略编号
        document.getElementById('new-strategy-code').value = generateStrategyCode();

        modal.classList.remove('hidden');
        setTimeout(() => {
          modalContent.classList.remove('scale-95', 'opacity-0');
          modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);
      });

      // 关闭模态框
      document.getElementById('close-strategy-modal').addEventListener('click', function() {
        closeModal();
      });

      document.getElementById('cancel-new-strategy').addEventListener('click', function() {
        closeModal();
      });

      // 保存草稿
      document.getElementById('save-draft-strategy').addEventListener('click', function() {
        alert('策略草稿已保存！');
      });

      // 确认创建策略
      document.getElementById('confirm-new-strategy').addEventListener('click', function() {
        const strategyName = document.getElementById('new-strategy-name').value;
        const mainline = document.getElementById('new-strategy-mainline').value;
        const targetObject = document.getElementById('new-target-object').value;
        const summary = document.getElementById('new-strategy-summary').value;
        const scenario = document.getElementById('new-strategy-scenario').value;
        const relatedPolicy = document.getElementById('new-related-policy').value;

        if (!strategyName || !mainline || !targetObject || !summary || !scenario || !relatedPolicy) {
          alert('请填写必填项：策略名称、策略主线、适用对象、内容摘要、适用场景、关联政策依据');
          return;
        }

        alert('策略创建成功！已添加到策略库中。');
        closeModal();
      });

      // 查询按钮
      document.getElementById('btn-search').addEventListener('click', function() {
        const mainline = document.getElementById('strategy-mainline').value;
        const targetObject = document.getElementById('target-object').value;
        const strategyName = document.getElementById('strategy-name').value;
        const relatedPolicy = document.getElementById('related-policy').value;

        console.log('查询条件:', {
          mainline,
          targetObject,
          strategyName,
          relatedPolicy
        });

        alert('查询功能已触发，请查看控制台输出');
      });

      // 关闭模态框函数
      function closeModal() {
        const modal = document.getElementById('new-strategy-modal');
        const modalContent = document.getElementById('strategy-modal-content');

        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
          modal.classList.add('hidden');
          // 清空表单
          document.getElementById('new-strategy-name').value = '';
          document.getElementById('new-strategy-mainline').value = '';
          document.getElementById('new-target-object').value = '';
          document.getElementById('new-strategy-summary').value = '';
          document.getElementById('new-strategy-detail').value = '';
          document.getElementById('new-strategy-scenario').value = '';
          document.getElementById('new-related-policy').value = '';
          document.getElementById('new-policy-authority').value = '';
          document.getElementById('new-policy-date').value = '';
          document.getElementById('new-expected-effect').value = '';
          document.getElementById('new-implementation-period').value = '';
          document.getElementById('new-priority').value = '';
        }, 300);
      }
    });
  </script>
</body>

</html>
