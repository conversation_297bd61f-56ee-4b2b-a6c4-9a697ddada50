<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>脱贫成效分类</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .chart-container {
        position: relative;
        height: 100%;
        width: 100%;
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-pie-chart text-primary text-3xl"></i>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年7月17日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-text-primary mb-2">脱贫成效分类</h1>
      <p class="text-text-secondary">根据脱贫成效分类规则展示各类别村庄分布情况</p>
    </div>

    <!-- 仪表盘 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 饼图 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-4">成效分类分布</h3>
        <div class="h-[300px] chart-container">
          <canvas id="classification-chart"></canvas>
        </div>
      </div>

      <!-- 分类统计卡片 -->
      <div class="grid grid-cols-2 gap-4">
        <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
          <div class="flex justify-between items-start mb-4">
            <div>
              <p class="text-text-secondary">成效显著型</p>
              <h3 class="text-3xl font-bold mt-2 text-primary">42</h3>
            </div>
            <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
              <i class="fa fa-star text-xl"></i>
            </div>
          </div>
          <div class="text-text-tertiary text-sm">综合指数≥90分</div>
        </div>

        <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
          <div class="flex justify-between items-start mb-4">
            <div>
              <p class="text-text-secondary">稳步提升型</p>
              <h3 class="text-3xl font-bold mt-2 text-secondary">68</h3>
            </div>
            <div class="h-12 w-12 rounded-full bg-secondary/10 flex items-center justify-center text-secondary">
              <i class="fa fa-arrow-up text-xl"></i>
            </div>
          </div>
          <div class="text-text-tertiary text-sm">75-89分</div>
        </div>

        <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
          <div class="flex justify-between items-start mb-4">
            <div>
              <p class="text-text-secondary">潜力发展型</p>
              <h3 class="text-3xl font-bold mt-2 text-accent">31</h3>
            </div>
            <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
              <i class="fa fa-seedling text-xl"></i>
            </div>
          </div>
          <div class="text-text-tertiary text-sm">60-74分</div>
        </div>

        <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
          <div class="flex justify-between items-start mb-4">
            <div>
              <p class="text-text-secondary">需重点关注型</p>
              <h3 class="text-3xl font-bold mt-2 text-danger">15</h3>
            </div>
            <div class="h-12 w-12 rounded-full bg-danger/10 flex items-center justify-center text-danger">
              <i class="fa fa-exclamation-triangle text-xl"></i>
            </div>
          </div>
          <div class="text-text-tertiary text-sm">＜60分</div>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属地市</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部地市</option>
              <option value="chongqing">重庆主城</option>
              <option value="wanzhou">万州区</option>
              <option value="fuling">涪陵区</option>
              <option value="qianjiang">黔江区</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属区县</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部区县</option>
              <option value="wushan">巫山县</option>
              <option value="fengjie">奉节县</option>
              <option value="wuxi">巫溪县</option>
              <option value="shizhu">石柱县</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">成效分类</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部分类</option>
              <option value="excellent">成效显著型</option>
              <option value="stable">稳步提升型</option>
              <option value="potential">潜力发展型</option>
              <option value="attention">需重点关注型</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col justify-end">
          <button class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-2"></i> 查询
          </button>
        </div>
      </div>
    </div>

    <!-- 分类列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">村庄分类详情</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-primary/20 text-primary rounded-lg border border-primary/30 hover:bg-primary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full border-collapse border border-border">
          <thead>
            <tr class="bg-light">
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">村庄名称</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">所属地市</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">区县</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">综合指数得分</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">所属分类</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">主要特征</th>
            </tr>
          </thead>
          <tbody>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">龙凤村</td>
              <td class="border border-border px-4 py-3">万州区</td>
              <td class="border border-border px-4 py-3">龙驹镇</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">95.8</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary/10 text-primary">
                  <i class="fa fa-star mr-1"></i> 成效显著型
                </span>
              </td>
              <td class="border border-border px-4 py-3">产业发展迅速，人口稳定增长</td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">青山村</td>
              <td class="border border-border px-4 py-3">黔江区</td>
              <td class="border border-border px-4 py-3">石会镇</td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">94.2</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary/10 text-primary">
                  <i class="fa fa-star mr-1"></i> 成效显著型
                </span>
              </td>
              <td class="border border-border px-4 py-3">特色农业突出，基础设施完善</td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">桃花村</td>
              <td class="border border-border px-4 py-3">涪陵区</td>
              <td class="border border-border px-4 py-3">蔺市镇</td>
              <td class="border border-border px-4 py-3">
                <span class="text-secondary font-bold">82.7</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary/10 text-secondary">
                  <i class="fa fa-arrow-up mr-1"></i> 稳步提升型
                </span>
              </td>
              <td class="border border-border px-4 py-3">旅游业发展良好，收入持续增长</td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">金竹村</td>
              <td class="border border-border px-4 py-3">巫山县</td>
              <td class="border border-border px-4 py-3">大昌镇</td>
              <td class="border border-border px-4 py-3">
                <span class="text-accent font-bold">68.1</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-accent/10 text-accent">
                  <i class="fa fa-seedling mr-1"></i> 潜力发展型
                </span>
              </td>
              <td class="border border-border px-4 py-3">地理位置优越，发展潜力较大</td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 font-medium">梨花村</td>
              <td class="border border-border px-4 py-3">奉节县</td>
              <td class="border border-border px-4 py-3">白帝镇</td>
              <td class="border border-border px-4 py-3">
                <span class="text-danger font-bold">55.5</span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-danger/10 text-danger">
                  <i class="fa fa-exclamation-triangle mr-1"></i> 需重点关注型
                </span>
              </td>
              <td class="border border-border px-4 py-3">基础设施薄弱，人口外流严重</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 156 条，每页显示 5 条，第 1 页/共 32 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors" disabled>
            上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white rounded">1</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">2</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">3</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 创建饼图
      const ctx = document.getElementById('classification-chart').getContext('2d');
      const classificationChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['成效显著型', '稳步提升型', '潜力发展型', '需重点关注型'],
          datasets: [{
            data: [42, 68, 31, 15],
            backgroundColor: [
              '#31969A',
              '#80CCE3',
              '#8199C7',
              '#E74C3C'
            ],
            borderWidth: 0,
            hoverOffset: 10
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 20,
                usePointStyle: true,
                font: {
                  size: 14
                }
              }
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1E293B',
              bodyColor: '#64748B',
              borderColor: 'rgba(49, 150, 154, 0.2)',
              borderWidth: 1,
              padding: 16,
              callbacks: {
                label: function(context) {
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((context.raw / total) * 100).toFixed(1);
                  return `${context.label}: ${context.raw}个 (${percentage}%)`;
                }
              }
            }
          },
          cutout: '60%'
        }
      });
    });
  </script>
</body>

</html>
