<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 对口帮扶对象筛选</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .selection-badge {
        background: linear-gradient(135deg, #31969A, #80CCE3);
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
      }
      .village-row {
        transition: all 0.3s ease;
      }
      .village-row:hover {
        background-color: rgba(49, 150, 154, 0.05);
      }
      .village-row.selected {
        background-color: rgba(49, 150, 154, 0.1);
        border-left: 4px solid #31969A;
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-filter text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 对口帮扶对象筛选</h1>
          <p class="text-sm text-text-secondary">基于综合脱贫成效分析的精准帮扶对象筛选系统</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="selection-badge text-white px-3 py-1 rounded-full text-sm">
          <i class="fa fa-check-circle mr-1"></i> 智能筛选中
        </div>
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span class="text-text-primary">2025年8月5日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 筛选统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">候选村庄</p>
            <h3 class="text-3xl font-bold mt-2 text-primary">156</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-home text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">待筛选对象</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">已选定</p>
            <h3 class="text-3xl font-bold mt-2 text-success">42</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-success/10 flex items-center justify-center text-success">
            <i class="fa fa-check-circle text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">确定帮扶对象</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">候选中</p>
            <h3 class="text-3xl font-bold mt-2 text-warning">68</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-clock-o text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">评估中</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">高风险村</p>
            <h3 class="text-3xl font-bold mt-2 text-danger">23</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-danger/10 flex items-center justify-center text-danger">
            <i class="fa fa-exclamation-triangle text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">返贫风险预警</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">平均指数</p>
            <h3 class="text-3xl font-bold mt-2 text-accent">73.8</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-line-chart text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">综合脱贫指数</div>
      </div>
    </div>

    <!-- 筛选控制面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-text-primary">智能筛选条件</h2>
        <div class="flex space-x-3">
          <button id="btn-batch-select" class="px-4 py-2 bg-success text-white rounded-lg border border-success hover:bg-success/90 transition-colors">
            <i class="fa fa-check-square mr-2"></i> 批量选定
          </button>
          <button id="btn-reset-filter" class="px-4 py-2 bg-accent text-white rounded-lg border border-accent hover:bg-accent/90 transition-colors">
            <i class="fa fa-refresh mr-2"></i> 重置筛选
          </button>
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属地市</label>
          <div class="relative">
            <select id="filter-city" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部地市</option>
              <option value="wanzhou">万州区</option>
              <option value="fuling">涪陵区</option>
              <option value="qianjiang">黔江区</option>
              <option value="wushan">巫山县</option>
              <option value="fengjie">奉节县</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">区县</label>
          <div class="relative">
            <select id="filter-county" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部区县</option>
              <option value="longju">龙驹镇</option>
              <option value="shihui">石会镇</option>
              <option value="linshi">蔺市镇</option>
              <option value="dachang">大昌镇</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">返贫风险等级</label>
          <div class="relative">
            <select id="filter-risk" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部等级</option>
              <option value="high">高风险</option>
              <option value="medium">中风险</option>
              <option value="low">低风险</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">聚类类别</label>
          <div class="relative">
            <select id="filter-cluster" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部类别</option>
              <option value="type1">产业发展型</option>
              <option value="type2">基础薄弱型</option>
              <option value="type3">潜力提升型</option>
              <option value="type4">重点关注型</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">综合指数区间</label>
          <div class="relative">
            <select id="filter-score" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部区间</option>
              <option value="90-100">90-100分</option>
              <option value="80-89">80-89分</option>
              <option value="70-79">70-79分</option>
              <option value="60-69">60-69分</option>
              <option value="0-59">60分以下</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col justify-end">
          <button id="btn-search" class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-2"></i> 筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 筛选结果列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center space-x-4">
          <h3 class="text-xl font-semibold text-text-primary">帮扶对象筛选列表</h3>
          <div class="flex items-center space-x-2">
            <input type="checkbox" id="select-all" class="rounded border-border">
            <label for="select-all" class="text-sm text-text-secondary">全选</label>
          </div>
        </div>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-secondary/20 text-secondary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出列表
          </button>
          <button class="px-4 py-2 bg-warning/20 text-warning rounded-lg border border-warning/30 hover:bg-warning/30 transition-colors">
            <i class="fa fa-star mr-1"></i> 标记重点
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full border-collapse border border-border">
          <thead>
            <tr class="bg-light">
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">选择</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">村庄名称</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">所属地市</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">区县</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">综合指数得分</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">返贫风险等级</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">主要用能短板</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">发展潜力评估</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">当前状态</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">操作</th>
            </tr>
          </thead>
          <tbody id="village-list">
            <!-- 村庄数据将通过JavaScript动态生成 -->
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 对口帮扶对象筛选系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 模拟村庄数据
      const villageData = [
        {
          id: 1,
          name: '龙凤村',
          city: '万州区',
          county: '龙驹镇',
          score: 52.3,
          riskLevel: 'high',
          riskText: '高风险',
          shortboard: '产业用电不足',
          potential: '中等',
          status: 'candidate',
          statusText: '候选',
          cluster: 'type4'
        },
        {
          id: 2,
          name: '青山村',
          city: '黔江区',
          county: '石会镇',
          score: 78.6,
          riskLevel: 'medium',
          riskText: '中风险',
          shortboard: '基础设施薄弱',
          potential: '较高',
          status: 'selected',
          statusText: '已选定',
          cluster: 'type3'
        },
        {
          id: 3,
          name: '桃花村',
          city: '涪陵区',
          county: '蔺市镇',
          score: 85.2,
          riskLevel: 'low',
          riskText: '低风险',
          shortboard: '生活用电偏低',
          potential: '高',
          status: 'selected',
          statusText: '已选定',
          cluster: 'type1'
        },
        {
          id: 4,
          name: '金竹村',
          city: '巫山县',
          county: '大昌镇',
          score: 68.9,
          riskLevel: 'medium',
          riskText: '中风险',
          shortboard: '电网稳定性差',
          potential: '中等',
          status: 'pending',
          statusText: '待筛选',
          cluster: 'type2'
        },
        {
          id: 5,
          name: '梨花村',
          city: '奉节县',
          county: '白帝镇',
          score: 91.5,
          riskLevel: 'low',
          riskText: '低风险',
          shortboard: '无明显短板',
          potential: '很高',
          status: 'selected',
          statusText: '已选定',
          cluster: 'type1'
        },
        {
          id: 6,
          name: '翠竹村',
          city: '石柱县',
          county: '中益乡',
          score: 45.8,
          riskLevel: 'high',
          riskText: '高风险',
          shortboard: '整体用电水平低',
          potential: '低',
          status: 'candidate',
          statusText: '候选',
          cluster: 'type4'
        },
        {
          id: 7,
          name: '向阳村',
          city: '酉阳县',
          county: '花田乡',
          score: 73.4,
          riskLevel: 'medium',
          riskText: '中风险',
          shortboard: '农业用电不足',
          potential: '较高',
          status: 'candidate',
          statusText: '候选',
          cluster: 'type3'
        },
        {
          id: 8,
          name: '清泉村',
          city: '秀山县',
          county: '隘口镇',
          score: 82.1,
          riskLevel: 'low',
          riskText: '低风险',
          shortboard: '商业用电偏低',
          potential: '高',
          status: 'selected',
          statusText: '已选定',
          cluster: 'type1'
        },
        {
          id: 9,
          name: '红岩村',
          city: '彭水县',
          county: '三义乡',
          score: 59.7,
          riskLevel: 'high',
          riskText: '高风险',
          shortboard: '供电可靠性低',
          potential: '中等',
          status: 'pending',
          statusText: '待筛选',
          cluster: 'type2'
        },
        {
          id: 10,
          name: '松柏村',
          city: '城口县',
          county: '高观镇',
          score: 76.3,
          riskLevel: 'medium',
          riskText: '中风险',
          shortboard: '工业用电缺口',
          potential: '较高',
          status: 'candidate',
          statusText: '候选',
          cluster: 'type3'
        }
      ];

      let currentData = [...villageData];

      // 渲染村庄列表
      function renderVillageList(data) {
        const tbody = document.getElementById('village-list');
        tbody.innerHTML = '';

        data.forEach(village => {
          const row = document.createElement('tr');
          row.className = 'village-row border-b border-border hover:bg-light/50 transition-colors';

          // 状态样式
          const statusClass = {
            'pending': 'bg-text-tertiary/10 text-text-tertiary',
            'candidate': 'bg-warning/10 text-warning',
            'selected': 'bg-success/10 text-success'
          }[village.status];

          // 风险等级样式
          const riskClass = {
            'high': 'bg-danger/10 text-danger',
            'medium': 'bg-warning/10 text-warning',
            'low': 'bg-success/10 text-success'
          }[village.riskLevel];

          // 发展潜力样式
          const potentialClass = {
            '很高': 'bg-success/10 text-success',
            '高': 'bg-success/10 text-success',
            '较高': 'bg-warning/10 text-warning',
            '中等': 'bg-accent/10 text-accent',
            '低': 'bg-danger/10 text-danger'
          }[village.potential];

          row.innerHTML = `
            <td class="border border-border px-4 py-3">
              <input type="checkbox" class="village-checkbox rounded border-border" data-id="${village.id}">
            </td>
            <td class="border border-border px-4 py-3 font-medium">${village.name}</td>
            <td class="border border-border px-4 py-3">${village.city}</td>
            <td class="border border-border px-4 py-3">${village.county}</td>
            <td class="border border-border px-4 py-3">
              <span class="font-bold text-primary">${village.score}</span>
            </td>
            <td class="border border-border px-4 py-3">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs ${riskClass}">
                <i class="fa fa-${village.riskLevel === 'high' ? 'exclamation-triangle' : village.riskLevel === 'medium' ? 'exclamation-circle' : 'check-circle'} mr-1"></i>
                ${village.riskText}
              </span>
            </td>
            <td class="border border-border px-4 py-3 text-sm">${village.shortboard}</td>
            <td class="border border-border px-4 py-3">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs ${potentialClass}">
                ${village.potential}
              </span>
            </td>
            <td class="border border-border px-4 py-3">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs ${statusClass}">
                <i class="fa fa-${village.status === 'selected' ? 'check-circle' : village.status === 'candidate' ? 'clock-o' : 'circle-o'} mr-1"></i>
                ${village.statusText}
              </span>
            </td>
            <td class="border border-border px-4 py-3">
              <button class="text-primary hover:text-primary/70 mr-2" title="查看详情" onclick="viewDetails(${village.id})">
                <i class="fa fa-eye"></i>
              </button>
              <button class="text-success hover:text-success/70 mr-2" title="选定" onclick="selectVillage(${village.id})" ${village.status === 'selected' ? 'disabled' : ''}>
                <i class="fa fa-check"></i>
              </button>
              <button class="text-warning hover:text-warning/70" title="标记" onclick="markVillage(${village.id})">
                <i class="fa fa-star"></i>
              </button>
            </td>
          `;

          tbody.appendChild(row);
        });

        // 更新分页信息
        updatePagination(data.length);
      }

      // 更新分页信息
      function updatePagination(total) {
        const paginationDiv = document.querySelector('.pagination-info') || createPaginationInfo();
        paginationDiv.innerHTML = `
          <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
            <div class="text-text-secondary">
              共 ${total} 条，每页显示 10 条，第 1 页/共 ${Math.ceil(total/10)} 页
            </div>
            <div class="flex space-x-2">
              <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors" disabled>
                上一页
              </button>
              <button class="px-3 py-1 bg-primary text-white rounded">1</button>
              <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">
                下一页
              </button>
            </div>
          </div>
        `;
      }

      function createPaginationInfo() {
        const div = document.createElement('div');
        div.className = 'pagination-info';
        document.querySelector('.overflow-x-auto').parentNode.appendChild(div);
        return div;
      }

      // 筛选功能
      function filterVillages() {
        const city = document.getElementById('filter-city').value;
        const county = document.getElementById('filter-county').value;
        const risk = document.getElementById('filter-risk').value;
        const cluster = document.getElementById('filter-cluster').value;
        const score = document.getElementById('filter-score').value;

        currentData = villageData.filter(village => {
          if (city && !village.city.includes(city)) return false;
          if (county && !village.county.includes(county)) return false;
          if (risk && village.riskLevel !== risk) return false;
          if (cluster && village.cluster !== cluster) return false;
          if (score) {
            const [min, max] = score.split('-').map(Number);
            if (max && (village.score < min || village.score > max)) return false;
            if (!max && village.score >= min) return false;
          }
          return true;
        });

        renderVillageList(currentData);
      }

      // 全选功能
      document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.village-checkbox');
        checkboxes.forEach(cb => cb.checked = this.checked);
      });

      // 批量选定
      document.getElementById('btn-batch-select').addEventListener('click', function() {
        const selectedIds = Array.from(document.querySelectorAll('.village-checkbox:checked'))
          .map(cb => parseInt(cb.dataset.id));

        if (selectedIds.length === 0) {
          alert('请先选择要操作的村庄');
          return;
        }

        selectedIds.forEach(id => {
          const village = villageData.find(v => v.id === id);
          if (village && village.status !== 'selected') {
            village.status = 'selected';
            village.statusText = '已选定';
          }
        });

        renderVillageList(currentData);
        alert(`已成功选定 ${selectedIds.length} 个村庄作为帮扶对象`);
      });

      // 重置筛选
      document.getElementById('btn-reset-filter').addEventListener('click', function() {
        document.getElementById('filter-city').value = '';
        document.getElementById('filter-county').value = '';
        document.getElementById('filter-risk').value = '';
        document.getElementById('filter-cluster').value = '';
        document.getElementById('filter-score').value = '';
        currentData = [...villageData];
        renderVillageList(currentData);
      });

      // 搜索按钮
      document.getElementById('btn-search').addEventListener('click', filterVillages);

      // 操作函数
      window.viewDetails = function(id) {
        const village = villageData.find(v => v.id === id);
        alert(`查看 ${village.name} 详细信息\n综合指数：${village.score}\n风险等级：${village.riskText}\n发展潜力：${village.potential}`);
      };

      window.selectVillage = function(id) {
        const village = villageData.find(v => v.id === id);
        if (village && village.status !== 'selected') {
          village.status = 'selected';
          village.statusText = '已选定';
          renderVillageList(currentData);
          alert(`${village.name} 已选定为帮扶对象`);
        }
      };

      window.markVillage = function(id) {
        const village = villageData.find(v => v.id === id);
        alert(`${village.name} 已标记为重点关注对象`);
      };

      // 初始化渲染
      renderVillageList(currentData);
    });
  </script>
</body>

</html>
