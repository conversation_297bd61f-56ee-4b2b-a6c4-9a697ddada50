<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 脱贫成效多维评价指标体系构建 - 大模型能源电力政策信息挖掘系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .dimension-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
      }
      .dimension-card:hover {
        border-left-color: #31969A;
        background-color: rgba(49, 150, 154, 0.02);
      }
      .indicator-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-population { background-color: rgba(49, 150, 154, 0.1); color: #31969A; }
      .badge-industry { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-accent { background-color: rgba(129, 153, 199, 0.1); color: #8199C7; }
      .badge-core { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
      .badge-secondary { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-auxiliary { background-color: rgba(129, 153, 199, 0.1); color: #8199C7; }
      .weight-slider {
        -webkit-appearance: none;
        appearance: none;
        height: 6px;
        border-radius: 3px;
        background: #E2E8F0;
        outline: none;
      }
      .weight-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #31969A;
        cursor: pointer;
        border: 2px solid #FFFFFF;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .weight-slider::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #31969A;
        cursor: pointer;
        border: 2px solid #FFFFFF;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-cogs text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">2.1.1.3 脱贫成效多维评价指标体系构建</h1>
          <p class="text-sm text-text-secondary">可视化指标配置后台，从0到1构建科学多维的脱贫成效评价体系</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="w-full max-w-none mx-auto px-6 py-6">
    <!-- 指标体系概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">指标总数</p>
            <h3 class="text-3xl font-bold mt-2">28 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-list text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 16.7%
          </span>
          <span class="text-text-secondary ml-2">较上版本</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">维度分类数</p>
            <h3 class="text-3xl font-bold mt-2">5 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-sitemap text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 25.0%
          </span>
          <span class="text-text-secondary ml-2">较上版本</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">已配置公式</p>
            <h3 class="text-3xl font-bold mt-2">19 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-success/10 flex items-center justify-center text-success">
            <i class="fa fa-calculator text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 35.7%
          </span>
          <span class="text-text-secondary ml-2">较上版本</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">配置完整度</p>
            <h3 class="text-3xl font-bold mt-2">89.3 <span class="text-lg font-normal text-text-secondary">%</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-check-circle text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 12.8%
          </span>
          <span class="text-text-secondary ml-2">较上版本</span>
        </div>
      </div>
    </div>

    <!-- 指标配置后台 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">指标配置后台</h2>

      <!-- 基础信息配置 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-text-primary mb-4 border-b border-border pb-2">基础信息配置</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">指标名称 *</label>
            <input type="text" id="indicator-name" placeholder="请输入指标名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
          </div>

          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">所属维度 *</label>
            <div class="relative">
              <select id="indicator-dimension" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
                <option value="">请选择维度</option>
                <option value="产业发展">产业发展</option>
                <option value="生活水平">生活水平</option>
                <option value="人口活力">人口活力</option>
                <option value="基础设施">基础设施</option>
                <option value="环境质量">环境质量</option>
              </select>
              <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                <i class="fa fa-chevron-down text-text-secondary"></i>
              </div>
            </div>
          </div>

          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">数据单位 *</label>
            <input type="text" id="indicator-unit" placeholder="如：元、%、人、千瓦时等" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
          </div>
        </div>
      </div>

      <!-- 业务内涵配置 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-text-primary mb-4 border-b border-border pb-2">业务内涵配置</h3>
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">业务内涵描述 *</label>
          <textarea id="business-meaning" rows="3" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请详细描述该指标的业务含义、评价目的和应用场景"></textarea>
        </div>
      </div>

      <!-- 计算规则配置 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-text-primary mb-4 border-b border-border pb-2">计算规则配置</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">数据来源 *</label>
            <div class="relative">
              <select id="data-source" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
                <option value="">请选择数据来源</option>
                <option value="电力数据">电力数据</option>
                <option value="政府统计">政府统计</option>
                <option value="实地调研">实地调研</option>
                <option value="第三方数据">第三方数据</option>
                <option value="综合数据">综合数据</option>
              </select>
              <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                <i class="fa fa-chevron-down text-text-secondary"></i>
              </div>
            </div>
          </div>

          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">指标类型</label>
            <div class="relative">
              <select id="indicator-type" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
                <option value="">请选择类型</option>
                <option value="定量指标">定量指标</option>
                <option value="定性指标">定性指标</option>
                <option value="复合指标">复合指标</option>
              </select>
              <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                <i class="fa fa-chevron-down text-text-secondary"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">计算公式 *</label>
          <textarea id="calculation-formula" rows="4" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请输入指标计算公式，支持数学表达式和函数，如：(总收入 - 总支出) / 总人口"></textarea>
          <div class="text-sm text-text-tertiary mt-2">
            <i class="fa fa-info-circle mr-1"></i>
            支持基本数学运算符（+、-、*、/）、括号、常用函数（SUM、AVG、MAX、MIN等）
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button id="btn-search-indicator" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-1"></i> 查询指标
          </button>
          <button id="btn-reset-form" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 重置表单
          </button>
          <button id="btn-validate-formula" class="px-4 py-3 bg-accent text-white rounded-lg border border-accent hover:bg-accent/90 transition-colors">
            <i class="fa fa-check mr-1"></i> 验证公式
          </button>
        </div>
        <div class="flex space-x-3">
          <button id="btn-import-indicators" class="px-4 py-3 bg-secondary text-white rounded-lg border border-secondary hover:bg-secondary/90 transition-colors">
            <i class="fa fa-upload mr-1"></i> 批量导入
          </button>
          <button id="btn-add-indicator" class="px-4 py-3 bg-success text-white rounded-lg border border-success hover:bg-success/90 transition-colors">
            <i class="fa fa-plus mr-1"></i> 创建指标
          </button>
        </div>
      </div>
    </div>

    <!-- 维度分组管理 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">维度分组管理</h3>
        <div class="flex space-x-3">
          <button id="btn-add-dimension" class="px-4 py-2 bg-success/20 text-success rounded-lg border border-success/30 hover:bg-success/30 transition-colors">
            <i class="fa fa-plus mr-1"></i> 新增维度
          </button>
          <button class="px-4 py-2 bg-accent/20 text-accent rounded-lg border border-accent/30 hover:bg-accent/30 transition-colors">
            <i class="fa fa-cog mr-1"></i> 维度设置
          </button>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <!-- 产业发展维度 -->
        <div class="bg-light/50 border border-border rounded-xl p-5">
          <div class="flex justify-between items-center mb-4">
            <h4 class="text-lg font-semibold text-text-primary">产业发展</h4>
            <div class="flex items-center space-x-2">
              <span class="indicator-badge badge-industry">3个指标</span>
              <button class="text-primary hover:text-primary/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
          </div>

          <div class="space-y-3">
            <div class="flex items-center justify-between p-3 bg-lighter border border-border rounded-lg">
              <div class="flex items-center space-x-3">
                <span class="indicator-badge badge-core">产业总产值</span>
                <span class="text-sm text-text-secondary">万元</span>
              </div>
              <button class="text-accent hover:text-accent/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
            <div class="flex items-center justify-between p-3 bg-lighter border border-border rounded-lg">
              <div class="flex items-center space-x-3">
                <span class="indicator-badge badge-core">用电量增长率</span>
                <span class="text-sm text-text-secondary">%</span>
              </div>
              <button class="text-accent hover:text-accent/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
            <div class="flex items-center justify-between p-3 bg-lighter border border-border rounded-lg">
              <div class="flex items-center space-x-3">
                <span class="indicator-badge badge-secondary">企业数量</span>
                <span class="text-sm text-text-secondary">个</span>
              </div>
              <button class="text-accent hover:text-accent/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
          </div>
          <div class="mt-4 pt-3 border-t border-border">
            <p class="text-sm text-text-secondary">反映村庄产业发展规模和活力，基于电力数据分析产业振兴成效</p>
          </div>
        </div>

        <!-- 生活水平维度 -->
        <div class="bg-light/50 border border-border rounded-xl p-5">
          <div class="flex justify-between items-center mb-4">
            <h4 class="text-lg font-semibold text-text-primary">生活水平</h4>
            <div class="flex items-center space-x-2">
              <span class="indicator-badge badge-population">6个指标</span>
              <button class="text-primary hover:text-primary/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
          </div>
          <div class="space-y-3">
            <div class="flex items-center justify-between p-3 bg-lighter border border-border rounded-lg">
              <div class="flex items-center space-x-3">
                <span class="indicator-badge badge-core">人均可支配收入</span>
                <span class="text-sm text-text-secondary">元</span>
              </div>
              <button class="text-accent hover:text-accent/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
            <div class="flex items-center justify-between p-3 bg-lighter border border-border rounded-lg">
              <div class="flex items-center space-x-3">
                <span class="indicator-badge badge-core">住房条件改善率</span>
                <span class="text-sm text-text-secondary">%</span>
              </div>
              <button class="text-accent hover:text-accent/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
            <div class="flex items-center justify-between p-3 bg-lighter border border-border rounded-lg">
              <div class="flex items-center space-x-3">
                <span class="indicator-badge badge-secondary">医疗保障覆盖率</span>
                <span class="text-sm text-text-secondary">%</span>
              </div>
              <button class="text-accent hover:text-accent/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
            <!-- 展开查看更多提示 -->
            <div class="flex items-center justify-center p-2 bg-primary/5 border border-primary/20 rounded-lg cursor-pointer hover:bg-primary/10 transition-colors">
              <div class="flex items-center space-x-2 text-primary">
                <i class="fa fa-chevron-down text-sm"></i>
                <span class="text-sm font-medium">展开查看更多 (还有3个指标)</span>
                <i class="fa fa-chevron-down text-sm"></i>
              </div>
            </div>
          </div>
          <div class="mt-4 pt-3 border-t border-border">
            <p class="text-sm text-text-secondary">衡量村民收入水平和生活质量，直接反映脱贫攻坚成效</p>
          </div>
        </div>

        <!-- 人口活力维度 -->
        <div class="bg-light/50 border border-border rounded-xl p-5">
          <div class="flex justify-between items-center mb-4">
            <h4 class="text-lg font-semibold text-text-primary">人口活力</h4>
            <div class="flex items-center space-x-2">
              <span class="indicator-badge badge-accent">5个指标</span>
              <button class="text-primary hover:text-primary/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
          </div>
          <div class="space-y-3">
            <div class="flex items-center justify-between p-3 bg-lighter border border-border rounded-lg">
              <div class="flex items-center space-x-3">
                <span class="indicator-badge badge-core">就业率</span>
                <span class="text-sm text-text-secondary">%</span>
              </div>
              <button class="text-accent hover:text-accent/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
            <div class="flex items-center justify-between p-3 bg-lighter border border-border rounded-lg">
              <div class="flex items-center space-x-3">
                <span class="indicator-badge badge-core">人口流入率</span>
                <span class="text-sm text-text-secondary">%</span>
              </div>
              <button class="text-accent hover:text-accent/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
            <div class="flex items-center justify-between p-3 bg-lighter border border-border rounded-lg">
              <div class="flex items-center space-x-3">
                <span class="indicator-badge badge-secondary">受教育程度</span>
                <span class="text-sm text-text-secondary">年</span>
              </div>
              <button class="text-accent hover:text-accent/80 transition-colors">
                <i class="fa fa-edit"></i>
              </button>
            </div>
            <!-- 展开查看更多提示 -->
            <div class="flex items-center justify-center p-2 bg-accent/5 border border-accent/20 rounded-lg cursor-pointer hover:bg-accent/10 transition-colors">
              <div class="flex items-center space-x-2 text-accent">
                <i class="fa fa-chevron-down text-sm"></i>
                <span class="text-sm font-medium">展开查看更多 (还有2个指标)</span>
                <i class="fa fa-chevron-down text-sm"></i>
              </div>
            </div>
          </div>
          <div class="mt-4 pt-3 border-t border-border">
            <p class="text-sm text-text-secondary">评估人口结构和活力状况，反映村庄发展潜力和可持续性</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 指标详细列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">指标详细列表</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-secondary/20 text-secondary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出体系
          </button>
          <button class="px-4 py-2 bg-accent/20 text-accent rounded-lg border border-accent/30 hover:bg-accent/30 transition-colors">
            <i class="fa fa-save mr-1"></i> 保存版本
          </button>
        </div>
      </div>

      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full border border-border rounded-lg">
          <thead class="bg-light border-b border-border">
            <tr>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 5%;">
                <input type="checkbox" class="rounded border-border">
              </th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 18%;">指标名称</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">所属维度</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">单位</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 22%;">业务内涵</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">数据来源</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 20%;">计算公式</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary" style="width: 12%;">操作</th>
            </tr>
          </thead>
          <tbody id="indicator-table-body">
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">人均可支配收入</div>
                <div class="text-sm text-text-secondary mt-1">ID: IND_001</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="indicator-badge badge-population">生活水平</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">元</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">反映村民收入水平的核心指标，衡量脱贫攻坚成效的直接体现</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">政府统计</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm font-mono bg-light rounded px-2 py-1">总收入 / 总人口</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="复制">
                  <i class="fa fa-copy"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">产业用电量增长率</div>
                <div class="text-sm text-text-secondary mt-1">ID: IND_002</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="indicator-badge badge-industry">产业发展</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">%</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">基于电力数据分析产业发展活力，反映产业振兴和经济增长态势</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">电力数据</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm font-mono bg-light rounded px-2 py-1">(本期用电量 - 上期用电量) / 上期用电量 × 100%</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="复制">
                  <i class="fa fa-copy"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">就业率</div>
                <div class="text-sm text-text-secondary mt-1">ID: IND_003</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="indicator-badge badge-accent">人口活力</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">%</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">劳动力就业状况评估指标，反映村庄人口活力和经济发展水平</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">实地调研</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm font-mono bg-light rounded px-2 py-1">就业人数 / 劳动力总数 × 100%</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="复制">
                  <i class="fa fa-copy"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">产业总产值</div>
                <div class="text-sm text-text-secondary mt-1">ID: IND_004</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="indicator-badge badge-industry">产业发展</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">万元</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">村庄产业发展规模核心指标，衡量经济总量和产业振兴成效</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">政府统计</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm font-mono bg-light rounded px-2 py-1">第一产业产值 + 第二产业产值 + 第三产业产值</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="复制">
                  <i class="fa fa-copy"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">住房条件改善率</div>
                <div class="text-sm text-text-secondary mt-1">ID: IND_005</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="indicator-badge badge-population">生活水平</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">%</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">生活质量提升重要指标，反映脱贫攻坚在改善居住条件方面的成效</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">实地调研</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm font-mono bg-light rounded px-2 py-1">改善住房户数 / 总户数 × 100%</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="复制">
                  <i class="fa fa-copy"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">人口流入率</div>
                <div class="text-sm text-text-secondary mt-1">ID: IND_006</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="indicator-badge badge-accent">人口活力</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">%</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">反映村庄吸引力和发展活力，人口回流是乡村振兴的重要标志</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">政府统计</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm font-mono bg-light rounded px-2 py-1">流入人口数 / 常住人口数 × 100%</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="复制">
                  <i class="fa fa-copy"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">受教育程度</div>
                <div class="text-sm text-text-secondary mt-1">ID: IND_007</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="indicator-badge badge-accent">人口活力</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">年</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">人口素质评估指标，反映教育扶贫成效和人力资源发展水平</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">教育统计</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm font-mono bg-light rounded px-2 py-1">总受教育年限 / 总人口数</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="复制">
                  <i class="fa fa-copy"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 28 条，每页显示 10 条，第 1 页/共 3 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i> 上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">1</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">2</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">3</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">
            下一页 <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 大模型能源电力政策信息挖掘系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 查询指标按钮事件
      document.getElementById('btn-search-indicator').addEventListener('click', function () {
        const searchParams = {
          name: document.getElementById('indicator-name').value,
          dimension: document.getElementById('indicator-dimension').value,
          unit: document.getElementById('indicator-unit').value,
          source: document.getElementById('data-source').value
        };

        console.log('查询条件:', searchParams);
        alert('查询功能已触发，请查看控制台输出');
      });

      // 重置表单按钮事件
      document.getElementById('btn-reset-form').addEventListener('click', function () {
        document.getElementById('indicator-name').value = '';
        document.getElementById('indicator-dimension').value = '';
        document.getElementById('indicator-unit').value = '';
        document.getElementById('business-meaning').value = '';
        document.getElementById('data-source').value = '';
        document.getElementById('indicator-type').value = '';
        document.getElementById('calculation-formula').value = '';
      });

      // 验证公式按钮
      document.getElementById('btn-validate-formula').addEventListener('click', function () {
        const formula = document.getElementById('calculation-formula').value;
        if (!formula) {
          alert('请先输入计算公式');
          return;
        }

        // 简单的公式验证逻辑
        const validOperators = ['+', '-', '*', '/', '(', ')', 'SUM', 'AVG', 'MAX', 'MIN'];
        let isValid = true;

        // 这里可以添加更复杂的公式验证逻辑
        console.log('验证公式:', formula);

        if (isValid) {
          alert('公式验证通过！\n\n公式语法正确，可以正常使用');
        } else {
          alert('公式验证失败！\n\n请检查公式语法是否正确');
        }
      });

      // 批量导入按钮
      document.getElementById('btn-import-indicators').addEventListener('click', function () {
        alert('批量导入指标\n\n这里将打开文件选择对话框，支持Excel、CSV格式的指标数据导入');
      });

      // 创建指标按钮
      document.getElementById('btn-add-indicator').addEventListener('click', function () {
        const name = document.getElementById('indicator-name').value;
        const dimension = document.getElementById('indicator-dimension').value;
        const unit = document.getElementById('indicator-unit').value;
        const businessMeaning = document.getElementById('business-meaning').value;
        const source = document.getElementById('data-source').value;
        const formula = document.getElementById('calculation-formula').value;

        if (!name || !dimension || !unit || !businessMeaning || !source || !formula) {
          alert('请填写所有必填项：指标名称、所属维度、数据单位、业务内涵、数据来源、计算公式');
          return;
        }

        console.log('创建指标:', {
          name, dimension, unit, businessMeaning, source, formula
        });

        alert('指标创建成功！\n\n指标已添加到指标库中');
        // 这里可以添加实际的创建逻辑
      });

      // 新增维度按钮
      document.getElementById('btn-add-dimension').addEventListener('click', function () {
        const dimensionName = prompt('请输入新维度名称：');
        if (dimensionName) {
          alert(`维度"${dimensionName}"创建成功！\n\n可以在指标配置中选择该维度`);
          // 这里可以添加实际的维度创建逻辑
        }
      });

      // 表格操作按钮事件
      document.addEventListener('click', function(e) {
        if (e.target.closest('.fa-edit')) {
          const row = e.target.closest('tr');
          const indicatorName = row.cells[1].querySelector('.font-medium').textContent;
          alert(`编辑指标：${indicatorName}`);
        }

        if (e.target.closest('.fa-copy')) {
          const row = e.target.closest('tr');
          const indicatorName = row.cells[1].querySelector('.font-medium').textContent;
          alert(`复制指标：${indicatorName}\n\n指标信息已复制到剪贴板`);
        }

        if (e.target.closest('.fa-trash')) {
          const row = e.target.closest('tr');
          const indicatorName = row.cells[1].querySelector('.font-medium').textContent;
          if (confirm(`确定要删除指标"${indicatorName}"吗？`)) {
            alert('指标已删除');
          }
        }
      });

      // 导出体系按钮
      document.addEventListener('click', function(e) {
        if (e.target.closest('button')?.textContent.includes('导出体系')) {
          alert('导出指标体系\n\n将生成包含所有指标的Excel文件');
        }

        if (e.target.closest('button')?.textContent.includes('保存版本')) {
          alert('保存当前版本\n\n当前指标体系已保存为新版本');
        }
      });

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>
