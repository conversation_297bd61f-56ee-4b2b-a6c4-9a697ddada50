<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>脱贫成效预警监测</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .chart-container {
        position: relative;
        height: 200px;
        width: 100%;
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .pulse-animation {
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-exclamation-triangle text-primary text-3xl"></i>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年7月17日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-text-primary mb-2">脱贫成效预警监测</h1>
      <p class="text-text-secondary">实时监测村庄脱贫成效，及时发现风险预警信号</p>
    </div>

    <!-- 预警监测仪表盘 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- 预警总览 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-4">预警总览</h3>
        <div class="h-[200px] w-full relative">
          <canvas id="warning-overview-chart" class="max-h-[200px]"></canvas>
        </div>
      </div>

      <!-- 预警统计卡片 -->
      <div class="lg:col-span-2 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
          <div class="flex justify-between items-start mb-4">
            <div>
              <p class="text-text-secondary">预警总数</p>
              <h3 class="text-3xl font-bold mt-2 text-danger">23</h3>
            </div>
            <div class="h-12 w-12 rounded-full bg-danger/10 flex items-center justify-center text-danger pulse-animation">
              <i class="fa fa-bell text-xl"></i>
            </div>
          </div>
          <div class="text-text-tertiary text-sm">较上月 +5</div>
        </div>

        <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
          <div class="flex justify-between items-start mb-4">
            <div>
              <p class="text-text-secondary">高风险预警</p>
              <h3 class="text-3xl font-bold mt-2 text-danger">8</h3>
            </div>
            <div class="h-12 w-12 rounded-full bg-danger/10 flex items-center justify-center text-danger">
              <i class="fa fa-exclamation-circle text-xl"></i>
            </div>
          </div>
          <div class="text-text-tertiary text-sm">需立即处理</div>
        </div>

        <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
          <div class="flex justify-between items-start mb-4">
            <div>
              <p class="text-text-secondary">中风险预警</p>
              <h3 class="text-3xl font-bold mt-2 text-warning">10</h3>
            </div>
            <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
              <i class="fa fa-exclamation-triangle text-xl"></i>
            </div>
          </div>
          <div class="text-text-tertiary text-sm">需密切关注</div>
        </div>

        <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
          <div class="flex justify-between items-start mb-4">
            <div>
              <p class="text-text-secondary">低风险预警</p>
              <h3 class="text-3xl font-bold mt-2 text-secondary">5</h3>
            </div>
            <div class="h-12 w-12 rounded-full bg-secondary/10 flex items-center justify-center text-secondary">
              <i class="fa fa-info-circle text-xl"></i>
            </div>
          </div>
          <div class="text-text-tertiary text-sm">定期观察</div>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">预警等级</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部等级</option>
              <option value="high">高风险</option>
              <option value="medium">中风险</option>
              <option value="low">低风险</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">预警类型</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部类型</option>
              <option value="index">综合指数下降</option>
              <option value="industry">产业发展异常</option>
              <option value="population">人口流失严重</option>
              <option value="income">收入水平下滑</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属地市</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部地市</option>
              <option value="chongqing">重庆主城</option>
              <option value="wanzhou">万州区</option>
              <option value="fuling">涪陵区</option>
              <option value="qianjiang">黔江区</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col justify-end">
          <button class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-2"></i> 查询
          </button>
        </div>
      </div>
    </div>

    <!-- 预警详情列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">预警详情列表</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-danger/20 text-danger rounded-lg border border-danger/30 hover:bg-danger/30 transition-colors">
            <i class="fa fa-bell mr-1"></i> 批量处理
          </button>
          <button class="px-4 py-2 bg-primary/20 text-primary rounded-lg border border-primary/30 hover:bg-primary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full border-collapse border border-border">
          <thead>
            <tr class="bg-light">
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">预警等级</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">村庄名称</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">所属地市</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">区县</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">触发预警规则</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">具体预警内容</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">发生时间</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-danger/10 text-danger">
                  <i class="fa fa-exclamation-circle mr-1"></i> 高风险
                </span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">石梁村</td>
              <td class="border border-border px-4 py-3">巫溪县</td>
              <td class="border border-border px-4 py-3">文峰镇</td>
              <td class="border border-border px-4 py-3">综合指数连续下降</td>
              <td class="border border-border px-4 py-3">综合指数连续3个月下降，当前得分52.3</td>
              <td class="border border-border px-4 py-3">2025-07-15</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-bell-slash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-warning/10 text-warning">
                  <i class="fa fa-exclamation-triangle mr-1"></i> 中风险
                </span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">双河村</td>
              <td class="border border-border px-4 py-3">城口县</td>
              <td class="border border-border px-4 py-3">坪坝镇</td>
              <td class="border border-border px-4 py-3">人口流失严重</td>
              <td class="border border-border px-4 py-3">近6个月人口流失率达15%，青壮年外流明显</td>
              <td class="border border-border px-4 py-3">2025-07-14</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-bell-slash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-danger/10 text-danger">
                  <i class="fa fa-exclamation-circle mr-1"></i> 高风险
                </span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">柏林村</td>
              <td class="border border-border px-4 py-3">彭水县</td>
              <td class="border border-border px-4 py-3">保家镇</td>
              <td class="border border-border px-4 py-3">产业发展异常</td>
              <td class="border border-border px-4 py-3">主导产业收益下滑40%，农户收入大幅减少</td>
              <td class="border border-border px-4 py-3">2025-07-13</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-bell-slash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary/10 text-secondary">
                  <i class="fa fa-info-circle mr-1"></i> 低风险
                </span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">花园村</td>
              <td class="border border-border px-4 py-3">酉阳县</td>
              <td class="border border-border px-4 py-3">龙潭镇</td>
              <td class="border border-border px-4 py-3">收入水平下滑</td>
              <td class="border border-border px-4 py-3">人均收入较去年同期下降8%，需持续观察</td>
              <td class="border border-border px-4 py-3">2025-07-12</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-bell-slash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-warning/10 text-warning">
                  <i class="fa fa-exclamation-triangle mr-1"></i> 中风险
                </span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">松林村</td>
              <td class="border border-border px-4 py-3">秀山县</td>
              <td class="border border-border px-4 py-3">洪安镇</td>
              <td class="border border-border px-4 py-3">综合指数连续下降</td>
              <td class="border border-border px-4 py-3">综合指数连续2个月下降，当前得分65.7</td>
              <td class="border border-border px-4 py-3">2025-07-11</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-bell-slash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-danger/10 text-danger">
                  <i class="fa fa-exclamation-circle mr-1"></i> 高风险
                </span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">翠竹村</td>
              <td class="border border-border px-4 py-3">石柱县</td>
              <td class="border border-border px-4 py-3">中益乡</td>
              <td class="border border-border px-4 py-3">产业发展异常</td>
              <td class="border border-border px-4 py-3">主导产业遭受自然灾害，产量下降60%</td>
              <td class="border border-border px-4 py-3">2025-07-10</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-bell-slash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-warning/10 text-warning">
                  <i class="fa fa-exclamation-triangle mr-1"></i> 中风险
                </span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">向阳村</td>
              <td class="border border-border px-4 py-3">酉阳县</td>
              <td class="border border-border px-4 py-3">花田乡</td>
              <td class="border border-border px-4 py-3">人口流失严重</td>
              <td class="border border-border px-4 py-3">近3个月人口流失率达12%，劳动力不足</td>
              <td class="border border-border px-4 py-3">2025-07-09</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-bell-slash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary/10 text-secondary">
                  <i class="fa fa-info-circle mr-1"></i> 低风险
                </span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">清泉村</td>
              <td class="border border-border px-4 py-3">秀山县</td>
              <td class="border border-border px-4 py-3">隘口镇</td>
              <td class="border border-border px-4 py-3">收入水平下滑</td>
              <td class="border border-border px-4 py-3">人均收入较去年同期下降5%，波动正常</td>
              <td class="border border-border px-4 py-3">2025-07-08</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-bell-slash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-warning/10 text-warning">
                  <i class="fa fa-exclamation-triangle mr-1"></i> 中风险
                </span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">红岩村</td>
              <td class="border border-border px-4 py-3">彭水县</td>
              <td class="border border-border px-4 py-3">三义乡</td>
              <td class="border border-border px-4 py-3">综合指数连续下降</td>
              <td class="border border-border px-4 py-3">综合指数连续2个月下降，当前得分68.9</td>
              <td class="border border-border px-4 py-3">2025-07-07</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-bell-slash"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary/10 text-secondary">
                  <i class="fa fa-info-circle mr-1"></i> 低风险
                </span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">松柏村</td>
              <td class="border border-border px-4 py-3">城口县</td>
              <td class="border border-border px-4 py-3">高观镇</td>
              <td class="border border-border px-4 py-3">收入水平下滑</td>
              <td class="border border-border px-4 py-3">人均收入较去年同期下降6%，需持续观察</td>
              <td class="border border-border px-4 py-3">2025-07-06</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/70 mr-2">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-danger hover:text-danger/70">
                  <i class="fa fa-bell-slash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 23 条，每页显示 10 条，第 1 页/共 3 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors" disabled>
            上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white rounded">1</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">2</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">3</button>
          <button class="px-3 py-1 border border-border rounded text-text-secondary hover:bg-light transition-colors">
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 创建预警总览图表
      const ctx = document.getElementById('warning-overview-chart').getContext('2d');
      const warningOverviewChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['高风险', '中风险', '低风险'],
          datasets: [{
            data: [8, 10, 5],
            backgroundColor: [
              '#E74C3C',
              '#E5CE66',
              '#80CCE3'
            ],
            borderWidth: 0,
            hoverOffset: 8
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          aspectRatio: 1,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 15,
                usePointStyle: true,
                font: {
                  size: 12
                }
              }
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1E293B',
              bodyColor: '#64748B',
              borderColor: 'rgba(49, 150, 154, 0.2)',
              borderWidth: 1,
              padding: 12,
              callbacks: {
                label: function(context) {
                  return `${context.label}: ${context.raw}个`;
                }
              }
            }
          },
          cutout: '50%'
        }
      });
    });
  </script>
</body>

</html>
