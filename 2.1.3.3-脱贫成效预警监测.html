<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>脱贫成效预警监测系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .table-border {
        border: 1px solid #E2E8F0;
      }
      .table-border th,
      .table-border td {
        border: 1px solid #E2E8F0;
      }
      .pulse-animation {
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-shield text-primary text-3xl"></i>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月5日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 控制面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-2xl font-semibold text-text-primary mb-6">脱贫成效预警监测</h2>
      <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属地市</label>
          <div class="relative">
            <select id="city-select" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部地市</option>
              <option value="chongqing">重庆主城</option>
              <option value="wanzhou">万州区</option>
              <option value="fuling">涪陵区</option>
              <option value="yubei">渝北区</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属区县</label>
          <div class="relative">
            <select id="county-select" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部区县</option>
              <option value="wushan">巫山县</option>
              <option value="fengjie">奉节县</option>
              <option value="wuxi">巫溪县</option>
              <option value="shizhu">石柱县</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">预警等级</label>
          <div class="relative">
            <select id="alert-level-select" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部等级</option>
              <option value="high">高风险</option>
              <option value="medium">中风险</option>
              <option value="low">低风险</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">预警指标</label>
          <div class="relative">
            <select id="indicator-select" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部指标</option>
              <option value="population">人口流失率</option>
              <option value="income">收入下降率</option>
              <option value="industry">产业发展指数</option>
              <option value="employment">就业率</option>
              <option value="infrastructure">基础设施指数</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">操作</label>
          <div class="flex space-x-3">
            <button id="btn-search" class="flex-1 px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
              <i class="fa fa-search mr-1"></i> 查询
            </button>
            <button id="btn-export" class="px-4 py-3 bg-primary/20 text-primary rounded-lg border border-primary/30 hover:bg-primary/30 transition-colors">
              <i class="fa fa-download mr-1"></i> 导出
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 预警统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">高风险预警</p>
            <h3 class="text-3xl font-bold mt-2 text-danger">8 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-danger/10 flex items-center justify-center text-danger pulse-animation">
            <i class="fa fa-exclamation-triangle text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-danger flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 2个
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">中风险预警</p>
            <h3 class="text-3xl font-bold mt-2 text-warning">15 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-warning text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-warning flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 3个
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">低风险预警</p>
            <h3 class="text-3xl font-bold mt-2 text-accent">12 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-info-circle text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-primary flex items-center">
            <i class="fa fa-arrow-down mr-1"></i> 1个
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">已处理预警</p>
            <h3 class="text-3xl font-bold mt-2 text-primary">28 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-check-circle text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-primary flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 5个
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>
    </div>

    <!-- 脱贫成效预警监测表格 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">预警监测列表</h3>
        <span class="text-text-secondary">动态监测返贫风险预警信息</span>
      </div>
      
      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full table-border">
          <thead>
            <tr class="bg-light">
              <th class="px-4 py-3 text-left text-text-primary font-semibold">预警等级</th>
              <th class="px-4 py-3 text-left text-text-primary font-semibold">地市</th>
              <th class="px-4 py-3 text-left text-text-primary font-semibold">区县</th>
              <th class="px-4 py-3 text-left text-text-primary font-semibold">村名称</th>
              <th class="px-4 py-3 text-left text-text-primary font-semibold">预警指标</th>
              <th class="px-4 py-3 text-left text-text-primary font-semibold">当前值</th>
              <th class="px-4 py-3 text-left text-text-primary font-semibold">预警阈值</th>
              <th class="px-4 py-3 text-left text-text-primary font-semibold">预警时间</th>
              <th class="px-4 py-3 text-left text-text-primary font-semibold">处理状态</th>
            </tr>
          </thead>
          <tbody>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="px-4 py-3">
                <span class="bg-danger text-white px-3 py-1 rounded-full text-sm pulse-animation">高风险</span>
              </td>
              <td class="px-4 py-3">万州区</td>
              <td class="px-4 py-3">彭水县</td>
              <td class="px-4 py-3">三义乡莲花村</td>
              <td class="px-4 py-3">人口流失率</td>
              <td class="px-4 py-3 text-danger font-bold">18.5%</td>
              <td class="px-4 py-3">≤15%</td>
              <td class="px-4 py-3 text-text-secondary">2025-08-03</td>
              <td class="px-4 py-3">
                <span class="bg-warning/20 text-warning px-2 py-1 rounded text-sm">待处理</span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="px-4 py-3">
                <span class="bg-danger text-white px-3 py-1 rounded-full text-sm pulse-animation">高风险</span>
              </td>
              <td class="px-4 py-3">涪陵区</td>
              <td class="px-4 py-3">城口县</td>
              <td class="px-4 py-3">高观镇双竹村</td>
              <td class="px-4 py-3">收入下降率</td>
              <td class="px-4 py-3 text-danger font-bold">22.3%</td>
              <td class="px-4 py-3">≤20%</td>
              <td class="px-4 py-3 text-text-secondary">2025-08-02</td>
              <td class="px-4 py-3">
                <span class="bg-accent/20 text-accent px-2 py-1 rounded text-sm">处理中</span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="px-4 py-3">
                <span class="bg-warning text-white px-3 py-1 rounded-full text-sm">中风险</span>
              </td>
              <td class="px-4 py-3">重庆主城</td>
              <td class="px-4 py-3">石柱县</td>
              <td class="px-4 py-3">中益乡华溪村</td>
              <td class="px-4 py-3">产业发展指数</td>
              <td class="px-4 py-3 text-warning font-bold">68.2</td>
              <td class="px-4 py-3">≥70</td>
              <td class="px-4 py-3 text-text-secondary">2025-08-01</td>
              <td class="px-4 py-3">
                <span class="bg-primary/20 text-primary px-2 py-1 rounded text-sm">已处理</span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="px-4 py-3">
                <span class="bg-warning text-white px-3 py-1 rounded-full text-sm">中风险</span>
              </td>
              <td class="px-4 py-3">万州区</td>
              <td class="px-4 py-3">巫溪县</td>
              <td class="px-4 py-3">文峰镇银杏村</td>
              <td class="px-4 py-3">就业率</td>
              <td class="px-4 py-3 text-warning font-bold">72.8%</td>
              <td class="px-4 py-3">≥75%</td>
              <td class="px-4 py-3 text-text-secondary">2025-07-31</td>
              <td class="px-4 py-3">
                <span class="bg-warning/20 text-warning px-2 py-1 rounded text-sm">待处理</span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="px-4 py-3">
                <span class="bg-accent text-white px-3 py-1 rounded-full text-sm">低风险</span>
              </td>
              <td class="px-4 py-3">涪陵区</td>
              <td class="px-4 py-3">奉节县</td>
              <td class="px-4 py-3">白帝镇八阵村</td>
              <td class="px-4 py-3">基础设施指数</td>
              <td class="px-4 py-3 text-accent font-bold">78.5</td>
              <td class="px-4 py-3">≥80</td>
              <td class="px-4 py-3 text-text-secondary">2025-07-30</td>
              <td class="px-4 py-3">
                <span class="bg-accent/20 text-accent px-2 py-1 rounded text-sm">处理中</span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="px-4 py-3">
                <span class="bg-danger text-white px-3 py-1 rounded-full text-sm pulse-animation">高风险</span>
              </td>
              <td class="px-4 py-3">万州区</td>
              <td class="px-4 py-3">酉阳县</td>
              <td class="px-4 py-3">花田乡何家岩村</td>
              <td class="px-4 py-3">收入下降率</td>
              <td class="px-4 py-3 text-danger font-bold">25.7%</td>
              <td class="px-4 py-3">≤20%</td>
              <td class="px-4 py-3 text-text-secondary">2025-07-29</td>
              <td class="px-4 py-3">
                <span class="bg-warning/20 text-warning px-2 py-1 rounded text-sm">待处理</span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="px-4 py-3">
                <span class="bg-warning text-white px-3 py-1 rounded-full text-sm">中风险</span>
              </td>
              <td class="px-4 py-3">重庆主城</td>
              <td class="px-4 py-3">秀山县</td>
              <td class="px-4 py-3">隘口镇新院村</td>
              <td class="px-4 py-3">产业发展指数</td>
              <td class="px-4 py-3 text-warning font-bold">69.1</td>
              <td class="px-4 py-3">≥70</td>
              <td class="px-4 py-3 text-text-secondary">2025-07-28</td>
              <td class="px-4 py-3">
                <span class="bg-primary/20 text-primary px-2 py-1 rounded text-sm">已处理</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-6 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 63 条，每页显示 10 条，第 1 页/共 7 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-2 bg-light border border-border rounded-lg text-text-secondary hover:bg-light/70 transition-colors">
            <i class="fa fa-chevron-left"></i>
          </button>
          <button class="px-3 py-2 bg-primary text-white border border-primary rounded-lg">1</button>
          <button class="px-3 py-2 bg-light border border-border rounded-lg text-text-secondary hover:bg-light/70 transition-colors">2</button>
          <button class="px-3 py-2 bg-light border border-border rounded-lg text-text-secondary hover:bg-light/70 transition-colors">3</button>
          <button class="px-3 py-2 bg-light border border-border rounded-lg text-text-secondary hover:bg-light/70 transition-colors">
            <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 查询按钮事件
      document.getElementById('btn-search').addEventListener('click', function () {
        const city = document.getElementById('city-select').value;
        const county = document.getElementById('county-select').value;
        const alertLevel = document.getElementById('alert-level-select').value;
        const indicator = document.getElementById('indicator-select').value;
        
        console.log(`查询条件: 地市=${city}, 区县=${county}, 预警等级=${alertLevel}, 指标=${indicator}`);
        // 这里可以添加查询逻辑
      });

      // 导出按钮事件
      document.getElementById('btn-export').addEventListener('click', function () {
        console.log('导出脱贫成效预警监测数据');
        // 这里可以添加导出逻辑
      });

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>
